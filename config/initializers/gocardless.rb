Rails.application.configure do
  config.gocardless_providers = []

  # Primary GoCardless configuration
  if ENV["GOCARDLESS_SECRET_ID"].present? && ENV["GOCARDLESS_SECRET_KEY"].present?
    config.gocardless_providers << {
      name: "primary",
      secret_id: ENV["GOCARDLESS_SECRET_ID"],
      secret_key: ENV["GOCARDLESS_SECRET_KEY"],
      environment: ENV["GOCARDLESS_ENV"] || "sandbox"
    }
  end

  # Backup GoCardless configuration
  if ENV["GOCARDLESS_BACKUP_SECRET_ID"].present? && ENV["GOCARDLESS_BACKUP_SECRET_KEY"].present?
    config.gocardless_providers << {
      name: "backup",
      secret_id: ENV["GOCARDLESS_BACKUP_SECRET_ID"],
      secret_key: ENV["GOCARDLESS_BACKUP_SECRET_KEY"],
      environment: ENV["GOCARDLESS_BACKUP_ENV"] || ENV["GOCARDLESS_ENV"] || "sandbox"
    }
  end

  # Third backup GoCardless configuration
  if ENV["GOCARDLESS_BACKUP2_SECRET_ID"].present? && ENV["GOCARDLESS_BACKUP2_SECRET_KEY"].present?
    config.gocardless_providers << {
      name: "backup2",
      secret_id: ENV["GOCARDLESS_BACKUP2_SECRET_ID"],
      secret_key: ENV["GOCARDLESS_BACKUP2_SECRET_KEY"],
      environment: ENV["GOCARDLESS_BACKUP2_ENV"] || ENV["GOCARDLESS_ENV"] || "sandbox"
    }
  end

  # Legacy single provider config for backward compatibility
  config.gocardless = config.gocardless_providers.first
end