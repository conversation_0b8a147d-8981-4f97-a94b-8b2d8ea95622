Rails.application.configure do
  config.gocardless = nil
  config.gocardless_backup = nil

  # Primary GoCardless configuration
  if ENV["GOCARDLESS_SECRET_ID"].present? && ENV["GOCARDLESS_SECRET_KEY"].present?
    config.gocardless = {
      secret_id: ENV["GOCARDLESS_SECRET_ID"],
      secret_key: ENV["GOCARDLESS_SECRET_KEY"],
      environment: ENV["GOCARDLESS_ENV"] || "sandbox"
    }
  end

  # Backup GoCardless configuration
  if ENV["GOCARDLESS_BACKUP_SECRET_ID"].present? && ENV["GOCARDLESS_BACKUP_SECRET_KEY"].present?
    config.gocardless_backup = {
      secret_id: ENV["GOCARDLESS_BACKUP_SECRET_ID"],
      secret_key: ENV["GOCARDLESS_BACKUP_SECRET_KEY"],
      environment: <PERSON>NV["GOCARDLESS_BACKUP_ENV"] || ENV["GOCARDLESS_ENV"] || "sandbox"
    }
  end
end