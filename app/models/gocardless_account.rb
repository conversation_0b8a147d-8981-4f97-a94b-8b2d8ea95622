class GocardlessAccount < ApplicationRecord
  include Syncable

  validates :gocardless_account_id, presence: true, uniqueness: true

  belongs_to :gocardless_item
  belongs_to :account
  has_one :family, through: :gocardless_item

  def sync_data
    return unless provider.present?

    Rails.logger.info "Syncing GoCardless account: #{gocardless_account_id}"

    # Sync balances first
    sync_balances

    # Sync transactions
    sync_transactions

    # Trigger account sync to recalculate balances
    account.sync_data

    Rails.logger.info "Completed syncing GoCardless account: #{gocardless_account_id}"
  rescue => error
    Rails.logger.error "Error syncing GoCardless account #{gocardless_account_id}: #{error.message}"
    raise error
  end

  private
    def provider
      @provider ||= Provider::Registry.get_provider(:gocardless)
    end

    def sync_balances
      Rails.logger.info "Syncing balances for account: #{gocardless_account_id}"
      
      balances_response = provider.get_account_balances(account_id: gocardless_account_id)
      
      unless balances_response.success?
        Rails.logger.error "Failed to fetch balances: #{balances_response.error&.message}"
        return
      end

      balances_data = balances_response.data["balances"] || []
      
      balances_data.each do |balance_data|
        create_balance_entry(balance_data)
      end
    end

    def sync_transactions
      Rails.logger.info "Syncing transactions for account: #{gocardless_account_id}"
      
      transactions_response = provider.get_account_transactions(account_id: gocardless_account_id)
      
      unless transactions_response.success?
        Rails.logger.error "Failed to fetch transactions: #{transactions_response.error&.message}"
        return
      end

      transactions_data = transactions_response.data.dig("transactions", "booked") || []
      
      transactions_data.each do |transaction_data|
        create_transaction_entry(transaction_data)
      end
    end

    def create_balance_entry(balance_data)
      # GoCardless balance structure:
      # {
      #   "balanceAmount": { "amount": "1000.00", "currency": "GBP" },
      #   "balanceType": "interimAvailable",
      #   "referenceDate": "2023-01-01"
      # }
      
      amount_data = balance_data["balanceAmount"]
      return unless amount_data.present?

      amount = amount_data["amount"].to_f
      currency = amount_data["currency"] || account.currency
      date = Date.parse(balance_data["referenceDate"]) rescue Date.current

      # Create a valuation entry to set the account balance
      account.entries.find_or_create_by(
        date: date,
        entryable_type: "Valuation"
      ) do |entry|
        entry.amount = -amount # Negative for assets (increases balance)
        entry.currency = currency
        entry.entryable = Valuation.new(value: amount)
      end
    end

    def create_transaction_entry(transaction_data)
      # GoCardless transaction structure:
      # {
      #   "transactionId": "unique-id",
      #   "transactionAmount": { "amount": "-50.00", "currency": "GBP" },
      #   "valueDate": "2023-01-01",
      #   "bookingDate": "2023-01-01",
      #   "remittanceInformationUnstructured": "Payment description"
      # }
      
      transaction_id = transaction_data["transactionId"]
      amount_data = transaction_data["transactionAmount"]
      
      return unless transaction_id.present? && amount_data.present?

      amount = amount_data["amount"].to_f
      currency = amount_data["currency"] || account.currency
      date = Date.parse(transaction_data["valueDate"] || transaction_data["bookingDate"]) rescue Date.current
      description = transaction_data["remittanceInformationUnstructured"] || "GoCardless Transaction"

      # Check if transaction already exists
      existing_entry = account.entries.joins(:transaction).find_by(
        transactions: { gocardless_transaction_id: transaction_id }
      )
      
      return if existing_entry.present?

      # Create transaction entry
      account.entries.create!(
        date: date,
        amount: amount, # GoCardless already provides signed amounts
        currency: currency,
        entryable: Transaction.new(
          name: description,
          gocardless_transaction_id: transaction_id
        )
      )
    end
end 