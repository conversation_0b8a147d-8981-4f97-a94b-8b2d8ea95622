class GocardlessAccount < ApplicationRecord
  include Syncable

  validates :gocardless_account_id, presence: true, uniqueness: true

  belongs_to :gocardless_item
  belongs_to :account
  has_one :family, through: :gocardless_item

  def sync_data
    return unless provider.present?

    Rails.logger.info "Syncing GoCardless account: #{gocardless_account_id}"

    # Sync balances first
    sync_balances

    # Add delay to avoid rate limiting
    sleep(1)

    # Sync transactions
    sync_transactions

    Rails.logger.info "Completed syncing GoCardless account: #{gocardless_account_id}"
  rescue => error
    Rails.logger.error "Error syncing GoCardless account #{gocardless_account_id}: #{error.message}"
    raise error
  end

  private
    def provider
      @provider ||= Provider::Registry.get_provider(:gocardless)
    end

    def sync_balances
      Rails.logger.info "Syncing balances for account: #{gocardless_account_id}"

      balances_response = provider.get_account_balances(gocardless_account_id)

      unless balances_response.success?
        if balances_response.error&.message&.include?("429") || balances_response.error&.message&.include?("rate limit")
          Rails.logger.warn "Rate limited - skipping balance sync for now: #{balances_response.error&.message}"
        else
          Rails.logger.error "Failed to fetch balances: #{balances_response.error&.message}"
        end
        return
      end

      balances_data = balances_response.data["balances"] || []

      balances_data.each do |balance_data|
        create_balance_entry(balance_data)
      end
    end

    def sync_transactions
      Rails.logger.info "Syncing transactions for account: #{gocardless_account_id}"

      # Determine date range for sync
      date_from = determine_sync_start_date
      date_to = Date.current

      Rails.logger.info "Fetching transactions from #{date_from} to #{date_to}"

      transactions_response = provider.get_account_transactions(
        gocardless_account_id,
        date_from: date_from,
        date_to: date_to
      )

      unless transactions_response.success?
        if transactions_response.error&.message&.include?("429") || transactions_response.error&.message&.include?("rate limit")
          Rails.logger.warn "Rate limited - skipping transaction sync for now: #{transactions_response.error&.message}"
        else
          Rails.logger.error "Failed to fetch transactions: #{transactions_response.error&.message}"
        end
        return
      end

      transactions_data = transactions_response.data.dig("transactions", "booked") || []
      Rails.logger.info "Processing #{transactions_data.count} transactions"

      transactions_data.each do |transaction_data|
        create_transaction_entry(transaction_data)
      end

      # Update last sync date for incremental syncs
      update_last_transaction_sync_date(date_to)
    end

    def create_balance_entry(balance_data)
      # GoCardless balance structure:
      # {
      #   "balanceAmount": { "amount": "1000.00", "currency": "GBP" },
      #   "balanceType": "interimAvailable",
      #   "referenceDate": "2023-01-01"
      # }
      
      amount_data = balance_data["balanceAmount"]
      return unless amount_data.present?

      amount = amount_data["amount"].to_f
      currency = amount_data["currency"] || account.currency
      date = Date.parse(balance_data["referenceDate"]) rescue Date.current

      # Create a valuation entry to set the account balance
      account.entries.find_or_create_by(
        date: date,
        entryable_type: "Valuation"
      ) do |entry|
        entry.amount = -amount # Negative for assets (increases balance)
        entry.currency = currency
        entry.entryable = Valuation.new(value: amount)
      end
    end

    def create_transaction_entry(transaction_data)
      # GoCardless transaction structure:
      # {
      #   "transactionId": "unique-id",
      #   "transactionAmount": { "amount": "-50.00", "currency": "GBP" },
      #   "valueDate": "2023-01-01",
      #   "bookingDate": "2023-01-01",
      #   "remittanceInformationUnstructured": "Payment description"
      # }
      
      transaction_id = transaction_data["transactionId"]
      amount_data = transaction_data["transactionAmount"]
      
      return unless transaction_id.present? && amount_data.present?

      amount = amount_data["amount"].to_f
      currency = amount_data["currency"] || account.currency
      date = Date.parse(transaction_data["valueDate"] || transaction_data["bookingDate"]) rescue Date.current
      description = transaction_data["remittanceInformationUnstructured"] || "GoCardless Transaction"

      # Check if transaction already exists
      existing_transaction = account.transactions.find_by(gocardless_transaction_id: transaction_id)
      
      return if existing_transaction.present?

      # Create transaction entry
      account.entries.create!(
        date: date,
        amount: amount, # GoCardless already provides signed amounts
        currency: currency,
        name: description,
        entryable: Transaction.new(
          gocardless_transaction_id: transaction_id
        )
      )
    end

    def determine_sync_start_date
      # Check if this is the first sync (no transactions exist)
      latest_transaction = account.transactions
        .joins(:entry)
        .where.not(gocardless_transaction_id: nil)
        .order("entries.date DESC")
        .first

      if latest_transaction.nil?
        # First sync: get maximum historical data (GoCardless typically allows 90 days)
        # Some banks may allow more, so we start with 2 years and let the API limit us
        2.years.ago.to_date
      else
        # Incremental sync: get transactions from the last sync date
        # Go back 1 day to catch any late-posting transactions
        latest_transaction.entry.date - 1.day
      end
    end

    def update_last_transaction_sync_date(date)
      # Store the last sync date for future reference
      # This could be stored in the gocardless_account record if needed
      Rails.logger.info "Last transaction sync completed for date: #{date}"
    end
end