class GocardlessItem < ApplicationRecord
  include Syncable

  enum :status, { active: "active", expired: "expired", error: "error" }, default: :active

  validates :name, :requisition_id, :institution_id, presence: true
  validates :requisition_id, uniqueness: true

  belongs_to :family
  has_many :gocardless_accounts, dependent: :destroy
  has_many :accounts, through: :gocardless_accounts

  scope :active, -> { where(status: :active) }
  scope :ordered, -> { order(created_at: :desc) }

  def sync_data
    return unless provider.present?

    Rails.logger.info "Syncing GoCardless item: #{name} (#{requisition_id})"

    # In development, create mock data if we hit rate limits
    if Rails.env.development? && should_use_mock_data?
      Rails.logger.info "Using mock data for development testing"
      create_mock_account_data
      update!(status: :active)
      return
    end

    # Fetch requisition to get account IDs
    requisition_response = provider.get_requisition(requisition_id: requisition_id)

    unless requisition_response.success?
      Rails.logger.error "Failed to fetch requisition: #{requisition_response.error&.message}"
      update!(status: :error)
      return
    end

    account_ids = requisition_response.data["accounts"] || []

    if account_ids.empty?
      Rails.logger.warn "No accounts found for requisition: #{requisition_id}"
      return
    end

    Rails.logger.info "Found #{account_ids.size} accounts to sync"

    account_ids.each_with_index do |account_id, index|
      # Add delay between accounts to avoid rate limiting
      sleep(2) if index > 0
      sync_account(account_id)
    end

    update!(status: :active)
  rescue => error
    Rails.logger.error "Error syncing GoCardless item #{requisition_id}: #{error.message}"
    update!(status: :error)
    raise error
  end

  def destroy_later
    update!(status: :expired)
    DestroyJob.perform_later(self)
  end

  private
    def provider
      @provider ||= Provider::Registry.get_provider(:gocardless)
    end

    def should_use_mock_data?
      # Use mock data if we're in development and the institution name suggests it's for testing
      name.include?("Starling") || name.include?("Test") || name.include?("🧪")
    end

    def create_mock_account_data
      Rails.logger.info "Creating mock account data for #{name}"

      # Create a mock account with proper accountable
      account = family.accounts.create!(
        name: "#{name} Current Account",
        accountable: Depository.new,
        balance: Money.new(125050, "GBP"), # £1,250.50
        currency: "GBP",
        subtype: "checking"
      )

      # Create a GoCardless account link
      gocardless_account = gocardless_accounts.create!(
        gocardless_account_id: "mock-#{SecureRandom.uuid}",
        account: account
      )

      # Create some mock transactions
      create_mock_transactions(account)

      Rails.logger.info "Created mock account: #{account.name} with balance #{account.balance}"
    end

    def create_mock_transactions(account)
      # Create some sample transactions via entries
      transactions_data = [
        { date: 3.days.ago, amount: -2500, description: "Grocery Store" },
        { date: 5.days.ago, amount: -1200, description: "Coffee Shop" },
        { date: 7.days.ago, amount: 50000, description: "Salary Deposit" },
        { date: 10.days.ago, amount: -3000, description: "Online Purchase" },
        { date: 12.days.ago, amount: -800, description: "Transport" }
      ]

      transactions_data.each do |tx_data|
        account.entries.create!(
          name: tx_data[:description],
          date: tx_data[:date],
          amount: Money.new(tx_data[:amount], "GBP"),
          currency: "GBP",
          entryable: Transaction.new
        )
      end

      Rails.logger.info "Created #{transactions_data.length} mock transactions"
    end

    def sync_account(account_id)
      Rails.logger.info "Syncing GoCardless account: #{account_id}"
      
      # Get or create GoCardless account
      gocardless_account = gocardless_accounts.find_or_initialize_by(gocardless_account_id: account_id)
      
      if gocardless_account.new_record?
        # Create corresponding Maybe account
        account = create_maybe_account(account_id)
        gocardless_account.account = account
        gocardless_account.save!
      end
      
      # Sync account data
      gocardless_account.sync_data
    end

    def create_maybe_account(account_id)
      # Fetch account details to determine account type and name
      details_response = provider.get_account_details(account_id: account_id)

      unless details_response.success?
        Rails.logger.error "Failed to fetch account details for #{account_id}: #{details_response.error&.message}"
        raise "Failed to fetch account details"
      end

      account_data = details_response.data
      account_name = account_data.dig("account", "name") ||
                     account_data.dig("account", "product") ||
                     "GoCardless Account"

      currency = account_data.dig("account", "currency") || "GBP"

      # Use the specified accountable type or default to Depository
      accountable_class = (accountable_type || "Depository").constantize

      # Create account with the specified type
      family.accounts.create!(
        name: account_name,
        accountable: accountable_class.new,
        currency: currency,
        balance: 0
      )
    end

    class DestroyJob < ApplicationJob
      def perform(gocardless_item)
        gocardless_item.destroy
      end
    end
end 