class GocardlessItem < ApplicationRecord
  include Syncable

  enum :status, { active: "active", expired: "expired", error: "error" }, default: :active

  validates :name, :requisition_id, :institution_id, presence: true
  validates :requisition_id, uniqueness: true

  belongs_to :family
  has_many :gocardless_accounts, dependent: :destroy
  has_many :accounts, through: :gocardless_accounts

  scope :active, -> { where(status: :active) }
  scope :ordered, -> { order(created_at: :desc) }

  def sync_data
    return unless provider.present?

    Rails.logger.info "Syncing GoCardless item: #{name} (#{requisition_id})"

    # Disabled mock data - always use real API data
    # if Rails.env.development? && should_use_mock_data?
    #   Rails.logger.info "Using mock data for development testing"
    #   create_mock_account_data
    #   update!(status: :active)
    #   return
    # end

    # Try to fetch requisition to get account IDs, but use cached data if API is unavailable
    account_ids = []

    requisition_response = provider.get_requisition(requisition_id: requisition_id)

    if requisition_response.success?
      account_ids = requisition_response.data["accounts"] || []
      Rails.logger.info "Fetched fresh account IDs from API: #{account_ids}"
    else
      # If API fails, try to use account IDs from the connection
      connection = family.gocardless_connections.find_by(requisition_id: requisition_id)
      if connection&.account_data.present?
        account_ids = connection.account_data
        Rails.logger.info "Using cached account IDs from connection: #{account_ids}"
      else
        Rails.logger.error "Failed to fetch requisition and no cached data available: #{requisition_response.error&.message}"
        update!(status: :error)
        return
      end
    end

    if account_ids.empty?
      Rails.logger.warn "No accounts found for requisition: #{requisition_id}"
      return
    end

    Rails.logger.info "Found #{account_ids.size} accounts to sync"

    account_ids.each_with_index do |account_id, index|
      # Add delay between accounts to avoid rate limiting
      sleep(2) if index > 0
      sync_account(account_id)
    end

    update!(status: :active)
  rescue => error
    Rails.logger.error "Error syncing GoCardless item #{requisition_id}: #{error.message}"
    update!(status: :error)
    raise error
  end

  def destroy_later
    update!(status: :expired)
    DestroyJob.perform_later(self)
  end

  private
    def provider
      @provider ||= Provider::Registry.get_provider(:gocardless)
    end

    def should_use_mock_data?
      # Use mock data if we're in development and the institution name suggests it's for testing
      name.include?("Starling") || name.include?("Test") || name.include?("🧪")
    end

    def create_mock_account_data
      Rails.logger.info "Creating mock account data for #{name}"

      # Create a mock account with proper accountable
      account = family.accounts.create!(
        name: "#{name} Current Account",
        accountable: Depository.new,
        balance: Money.new(125050, "GBP"), # £1,250.50
        currency: "GBP",
        subtype: "checking"
      )

      # Create a GoCardless account link
      gocardless_account = gocardless_accounts.create!(
        gocardless_account_id: "mock-#{SecureRandom.uuid}",
        account: account
      )

      # Create some mock transactions
      create_mock_transactions(account)

      Rails.logger.info "Created mock account: #{account.name} with balance #{account.balance}"
    end

    def create_mock_transactions(account)
      # Create some sample transactions via entries
      transactions_data = [
        { date: 3.days.ago, amount: -2500, description: "Grocery Store" },
        { date: 5.days.ago, amount: -1200, description: "Coffee Shop" },
        { date: 7.days.ago, amount: 50000, description: "Salary Deposit" },
        { date: 10.days.ago, amount: -3000, description: "Online Purchase" },
        { date: 12.days.ago, amount: -800, description: "Transport" }
      ]

      transactions_data.each do |tx_data|
        account.entries.create!(
          name: tx_data[:description],
          date: tx_data[:date],
          amount: Money.new(tx_data[:amount], "GBP"),
          currency: "GBP",
          entryable: Transaction.new
        )
      end

      Rails.logger.info "Created #{transactions_data.length} mock transactions"
    end

    def sync_account(account_id)
      Rails.logger.info "Syncing GoCardless account: #{account_id}"

      # Get or create GoCardless account
      gocardless_account = gocardless_accounts.find_or_initialize_by(gocardless_account_id: account_id)

      if gocardless_account.new_record?
        # Create corresponding Maybe account with basic info
        account = create_maybe_account(account_id)
        gocardless_account.account = account
        gocardless_account.save!
        Rails.logger.info "Created GoCardless account link: #{account_id} -> #{account.name}"
      end

      # Try to sync account data (balances and transactions)
      # This will gracefully handle rate limits
      begin
        gocardless_account.sync_data
      rescue => error
        Rails.logger.warn "Could not sync account data due to API limits: #{error.message}"
        Rails.logger.info "Account structure created, data sync will retry when API is available"
      end
    end

    def create_maybe_account(account_id)
      # Create account with default values first, update details later when API is available
      account_name = "#{name} Account"
      currency = "GBP" # Default for UK banks

      # Use the specified accountable type or default to Depository
      accountable_class = (accountable_type || "Depository").constantize

      # Create account with basic info - we'll update details in a separate sync when API is available
      account = family.accounts.create!(
        name: account_name,
        accountable: accountable_class.new,
        currency: currency,
        balance: 0
      )

      Rails.logger.info "Created basic account: #{account_name} (details will be updated when API is available)"
      account
    end

    def update_account_details(account, account_id)
      # This method can be called later when API quota is available
      details_response = provider.get_account_details(account_id: account_id)

      if details_response.success?
        account_data = details_response.data
        account_name = account_data.dig("account", "name") ||
                       account_data.dig("account", "product") ||
                       account.name

        currency = account_data.dig("account", "currency") || account.currency

        account.update!(
          name: account_name,
          currency: currency
        )

        Rails.logger.info "Updated account details: #{account_name}"
      else
        Rails.logger.warn "Could not update account details (API unavailable): #{details_response.error&.message}"
      end
    end

    class DestroyJob < ApplicationJob
      def perform(gocardless_item)
        gocardless_item.destroy
      end
    end
end 