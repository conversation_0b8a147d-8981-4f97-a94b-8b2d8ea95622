class GocardlessItem < ApplicationRecord
  include Syncable

  enum :status, { active: "active", expired: "expired", error: "error" }, default: :active

  validates :name, :requisition_id, :institution_id, presence: true
  validates :requisition_id, uniqueness: true

  belongs_to :family
  has_many :gocardless_accounts, dependent: :destroy
  has_many :accounts, through: :gocardless_accounts

  scope :active, -> { where(status: :active) }
  scope :ordered, -> { order(created_at: :desc) }

  def sync_data
    return unless provider.present?

    Rails.logger.info "Syncing GoCardless item: #{name} (#{requisition_id})"

    # Fetch requisition to get account IDs
    requisition_response = provider.get_requisition(requisition_id: requisition_id)
    
    unless requisition_response.success?
      Rails.logger.error "Failed to fetch requisition: #{requisition_response.error&.message}"
      update!(status: :error)
      return
    end

    account_ids = requisition_response.data["accounts"] || []
    
    if account_ids.empty?
      Rails.logger.warn "No accounts found for requisition: #{requisition_id}"
      return
    end

    Rails.logger.info "Found #{account_ids.size} accounts to sync"
    
    account_ids.each do |account_id|
      sync_account(account_id)
    end

    update!(status: :active)
  rescue => error
    Rails.logger.error "Error syncing GoCardless item #{requisition_id}: #{error.message}"
    update!(status: :error)
    raise error
  end

  def destroy_later
    update!(status: :expired)
    DestroyJob.perform_later(self)
  end

  private
    def provider
      @provider ||= Provider::Registry.get_provider(:gocardless)
    end

    def sync_account(account_id)
      Rails.logger.info "Syncing GoCardless account: #{account_id}"
      
      # Get or create GoCardless account
      gocardless_account = gocardless_accounts.find_or_initialize_by(gocardless_account_id: account_id)
      
      if gocardless_account.new_record?
        # Create corresponding Maybe account
        account = create_maybe_account(account_id)
        gocardless_account.account = account
        gocardless_account.save!
      end
      
      # Sync account data
      gocardless_account.sync_data
    end

    def create_maybe_account(account_id)
      # Fetch account details to determine account type and name
      details_response = provider.get_account_details(account_id: account_id)
      
      unless details_response.success?
        Rails.logger.error "Failed to fetch account details for #{account_id}: #{details_response.error&.message}"
        raise "Failed to fetch account details"
      end

      account_data = details_response.data
      account_name = account_data.dig("account", "name") || 
                     account_data.dig("account", "product") || 
                     "GoCardless Account"
      
      currency = account_data.dig("account", "currency") || "GBP"

      # Create a depository account (most common for GoCardless)
      family.accounts.create!(
        name: account_name,
        accountable: Depository.new,
        currency: currency,
        balance: 0
      )
    end

    class DestroyJob < ApplicationJob
      def perform(gocardless_item)
        gocardless_item.destroy
      end
    end
end 