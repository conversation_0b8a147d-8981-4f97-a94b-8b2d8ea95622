class GocardlessConnection::Syncer
  attr_reader :gocardless_connection

  def initialize(gocardless_connection)
    @gocardless_connection = gocardless_connection
  end

  def perform_sync(sync)
    Rails.logger.info("Syncing GoCardless connection: #{gocardless_connection.requisition_id}")
    
    # Call the existing sync_data method on the connection
    gocardless_connection.sync_data
    
    Rails.logger.info("Completed syncing GoCardless connection: #{gocardless_connection.requisition_id}")
  end

  def perform_post_sync
    # Trigger family auto-match transfers after sync
    gocardless_connection.family.auto_match_transfers!
  end
end
