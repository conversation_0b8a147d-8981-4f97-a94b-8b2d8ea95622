class Provider::Gocardless < Provider
  # Subclass so errors caught in this provider are raised as Provider::Gocardless::Error
  Error = Class.new(Provider::Error)

  BASE_URL = "https://bankaccountdata.gocardless.com/api/v2"

  def initialize(config = nil)
    config ||= Rails.application.config.gocardless

    @secret_id = config[:secret_id]
    @secret_key = config[:secret_key]
    @environment = config[:environment]
    @access_token = nil
    @refresh_token = nil
  end

  def healthy?
    with_provider_response do
      get_access_token.present?
    end
  end

  # ================================
  #          Authentication
  # ================================

  def get_access_token
    with_provider_response do
      response = client.post("token/new/") do |req|
        req.body = {
          secret_id: @secret_id,
          secret_key: @secret_key
        }.to_json
      end

      data = response.body
      @access_token = data["access"]
      @refresh_token = data["refresh"]
      data
    end
  end

  def refresh_access_token
    return get_access_token unless @refresh_token

    with_provider_response do
      response = client.post("token/refresh/") do |req|
        req.body = {
          refresh: @refresh_token
        }.to_json
      end

      data = response.body
      @access_token = data["access"]
      @refresh_token = data["refresh"] if data["refresh"] # New refresh token if provided
      data
    rescue => e
      # If refresh fails, get new token
      get_access_token.data
    end
  end

  # ================================
  #          Institutions
  # ================================

  def list_institutions(country: "GB")
    with_provider_response do
      response = authenticated_request(:get, "institutions/", params: { country: country })
      response.body
    end
  end

  # ================================
  #          Agreements
  # ================================

  def create_agreement(institution_id:, max_historical_days: 90, access_valid_for_days: 90, access_scope: ["balances", "details", "transactions"])
    with_provider_response do
      response = authenticated_request(:post, "agreements/enduser/", body: {
        institution_id: institution_id,
        max_historical_days: max_historical_days,
        access_valid_for_days: access_valid_for_days,
        access_scope: access_scope
      })
      response.body
    end
  end

  # ================================
  #          Requisitions
  # ================================

  def create_requisition(redirect_url:, agreement_id:, institution_id:, reference: nil)
    with_provider_response do
      body = {
        redirect_url: redirect_url,
        institution_id: institution_id,
        agreement_id: agreement_id
      }
      body[:reference] = reference if reference.present?

      Rails.logger.info "Creating requisition with body: #{body.inspect}"
      response = authenticated_request(:post, "requisitions/", body: body)
      response.body
    end
  end

  def get_requisition(requisition_id)
    with_provider_response do
      response = authenticated_request(:get, "requisitions/#{requisition_id}/")
      response.body
    end
  end

  # ================================
  #          Accounts
  # ================================

  def get_account_details(account_id)
    with_provider_response do
      response = authenticated_request(:get, "accounts/#{account_id}/details/")
      response.body
    end
  end

  def get_account_balances(account_id)
    with_provider_response do
      response = authenticated_request(:get, "accounts/#{account_id}/balances/")
      response.body
    end
  end

  def get_account_transactions(account_id, date_from: nil, date_to: nil)
    with_provider_response do
      params = {}
      params[:date_from] = date_from.strftime("%Y-%m-%d") if date_from
      params[:date_to] = date_to.strftime("%Y-%m-%d") if date_to
      
      response = authenticated_request(:get, "accounts/#{account_id}/transactions/", params: params)
      response.body
    end
  end

  def delete_requisition(requisition_id)
    with_provider_response do
      response = authenticated_request(:delete, "requisitions/#{requisition_id}/")
      { success: true }
    end
  end

  def get_agreement(agreement_id)
    with_provider_response do
      response = authenticated_request(:get, "agreements/enduser/#{agreement_id}/")
      response.body
    end
  end

  def delete_agreement(agreement_id)
    with_provider_response do
      response = authenticated_request(:delete, "agreements/enduser/#{agreement_id}/")
      { success: true }
    end
  end

  private
    attr_reader :secret_id, :secret_key, :environment

    def client
      @client ||= Faraday.new(url: BASE_URL) do |faraday|
        faraday.request :json
        faraday.response :json
        faraday.response :raise_error
        faraday.adapter Faraday.default_adapter

        # Add timeout configuration to prevent hanging requests
        faraday.options.timeout = 30      # 30 seconds total timeout
        faraday.options.open_timeout = 10 # 10 seconds to establish connection
      end
    end

    def authenticated_request(method, path, params: {}, body: nil)
      Rails.logger.info "GoCardless API Request: #{method.upcase} #{BASE_URL}/#{path}"
      Rails.logger.info "Request params: #{params.inspect}" if params.any?
      Rails.logger.info "Request body: #{body.inspect}" if body

      # Get access token if we don't have one
      unless @access_token
        Rails.logger.info "No access token, getting new one..."
        token_response = get_access_token
        unless token_response.success?
          Rails.logger.error "Failed to get access token: #{token_response.error&.message}"
          raise "Failed to authenticate with GoCardless"
        end
      end

      begin
        response = client.send(method, path) do |req|
          req.headers["Authorization"] = "Bearer #{@access_token}"
          req.params.merge!(params) if params.any?
          req.body = body.to_json if body
        end

        Rails.logger.info "GoCardless API Response: #{response.status}"
        Rails.logger.info "Response body: #{response.body.inspect}"

        response
      rescue Faraday::UnauthorizedError => e
        Rails.logger.warn "Unauthorized error, attempting token refresh..."
        # If unauthorized, try to refresh token and retry once
        if @refresh_token
          refresh_response = refresh_access_token
          unless refresh_response.success?
            Rails.logger.error "Failed to refresh token: #{refresh_response.error&.message}"
            raise
          end

          Rails.logger.info "Token refreshed, retrying request..."
          response = client.send(method, path) do |req|
            req.headers["Authorization"] = "Bearer #{@access_token}"
            req.params.merge!(params) if params.any?
            req.body = body.to_json if body
          end
        else
          Rails.logger.error "No refresh token available, cannot retry"
          raise
        end
      rescue Faraday::TooManyRequestsError => e
        Rails.logger.warn "Rate limit exceeded (429), waiting 60 seconds..."
        sleep(60)

        # Retry the request once
        Rails.logger.info "Retrying request after rate limit wait..."
        response = client.send(method, path) do |req|
          req.headers["Authorization"] = "Bearer #{@access_token}"
          req.params.merge!(params) if params.any?
          req.body = body.to_json if body
        end
      rescue => e
        Rails.logger.error "GoCardless API Error: #{e.class}: #{e.message}"
        raise
      end
    end
end 