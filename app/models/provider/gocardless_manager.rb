class Provider::GocardlessManager < Provider
  # Manager for multiple GoCardless providers with automatic failover
  Error = Class.new(Provider::Error)

  def initialize
    @providers = []
    @current_provider_index = 0
    @rate_limit_tracker = {}
    
    setup_providers
  end

  def healthy?
    available_providers.any?(&:healthy?)
  end

  # Delegate all GoCardless API methods to the current provider with automatic failover
  def get_access_token
    execute_with_failover(:get_access_token)
  end

  def refresh_access_token
    execute_with_failover(:refresh_access_token)
  end

  def list_institutions(country: "GB")
    execute_with_failover(:list_institutions, country: country)
  end

  def get_institution(institution_id)
    execute_with_failover(:get_institution, institution_id)
  end

  def create_agreement(institution_id:, max_historical_days: 90, access_valid_for_days: 90, access_scope: ["balances", "details", "transactions"])
    execute_with_failover(:create_agreement, 
      institution_id: institution_id,
      max_historical_days: max_historical_days,
      access_valid_for_days: access_valid_for_days,
      access_scope: access_scope
    )
  end

  def get_agreement(agreement_id)
    execute_with_failover(:get_agreement, agreement_id)
  end

  def create_requisition(redirect_url:, agreement_id:, institution_id:, reference: nil)
    execute_with_failover(:create_requisition,
      redirect_url: redirect_url,
      agreement_id: agreement_id,
      institution_id: institution_id,
      reference: reference
    )
  end

  def get_requisition(requisition_id)
    execute_with_failover(:get_requisition, requisition_id)
  end

  def get_account_details(account_id)
    execute_with_failover(:get_account_details, account_id)
  end

  def get_account_balances(account_id)
    execute_with_failover(:get_account_balances, account_id)
  end

  def get_account_transactions(account_id, date_from: nil, date_to: nil)
    execute_with_failover(:get_account_transactions, account_id, date_from: date_from, date_to: date_to)
  end

  # Provider management methods
  def current_provider
    available_providers[@current_provider_index]
  end

  def available_providers
    @providers.select(&:present?)
  end

  def provider_count
    available_providers.count
  end

  def switch_to_next_provider
    return false if available_providers.count <= 1

    @current_provider_index = (@current_provider_index + 1) % available_providers.count
    Rails.logger.info "Switched to GoCardless provider #{@current_provider_index + 1}/#{available_providers.count}"
    true
  end

  def reset_to_primary_provider
    @current_provider_index = 0
    Rails.logger.info "Reset to primary GoCardless provider"
  end

  def provider_status
    {
      total_providers: @providers.count,
      available_providers: available_providers.count,
      current_provider: @current_provider_index + 1,
      rate_limit_status: @rate_limit_tracker
    }
  end

  private

  def setup_providers
    # Primary provider
    if Rails.application.config.gocardless.present?
      @providers << Provider::Gocardless.new(Rails.application.config.gocardless)
      Rails.logger.info "GoCardless primary provider configured"
    end

    # Backup provider
    if Rails.application.config.gocardless_backup.present?
      @providers << Provider::Gocardless.new(Rails.application.config.gocardless_backup)
      Rails.logger.info "GoCardless backup provider configured"
    end

    Rails.logger.info "GoCardless manager initialized with #{@providers.count} providers"
  end

  def execute_with_failover(method, *args, **kwargs)
    attempts = 0
    max_attempts = available_providers.count

    while attempts < max_attempts
      provider = current_provider
      
      unless provider.present?
        Rails.logger.error "No GoCardless providers available"
        return Provider::Response.new(success: false, error: Provider::Error.new("No providers available"))
      end

      begin
        Rails.logger.debug "Executing #{method} with provider #{@current_provider_index + 1}/#{available_providers.count}"
        
        response = provider.send(method, *args, **kwargs)
        
        if response.success?
          # Reset rate limit tracking on successful request
          @rate_limit_tracker[@current_provider_index] = nil
          return response
        elsif rate_limited?(response)
          handle_rate_limit(response)
          attempts += 1
          next if switch_to_next_provider
        else
          # Non-rate-limit error, return immediately
          return response
        end

      rescue => e
        Rails.logger.error "GoCardless provider #{@current_provider_index + 1} error: #{e.message}"
        attempts += 1
        next if switch_to_next_provider
      end

      break
    end

    # All providers failed
    Rails.logger.error "All GoCardless providers failed for method: #{method}"
    Provider::Response.new(
      success: false, 
      error: Provider::Error.new("All GoCardless providers failed or rate limited")
    )
  end

  def rate_limited?(response)
    return false if response.success?
    
    error_message = response.error&.message || ""
    error_message.include?("429") || error_message.downcase.include?("rate limit")
  end

  def handle_rate_limit(response)
    @rate_limit_tracker[@current_provider_index] = {
      timestamp: Time.current,
      error: response.error&.message
    }
    
    Rails.logger.warn "GoCardless provider #{@current_provider_index + 1} rate limited: #{response.error&.message}"
  end
end
