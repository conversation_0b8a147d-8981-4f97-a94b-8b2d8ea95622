# Model to store GoCardless Bank Account Data connections
# Tracks the requisition flow and stores access tokens and account data
#
class GocardlessConnection < ApplicationRecord
  include Syncable

  belongs_to :family
  has_one :gocardless_item, dependent: :destroy
  
  # Requisition statuses from GoCardless API
  # CR - Created
  # GC - Giving Consent  
  # UA - Undergoing Authentication
  # RJ - Rejected
  # SA - Selecting Accounts
  # GA - Granting Access
  # LN - Linked
  # SU - Suspended
  # EX - Expired
  REQUISITION_STATUSES = %w[CR GC UA RJ SA GA LN SU EX].freeze
  
  validates :requisition_id, presence: true, uniqueness: true
  validates :institution_id, presence: true
  validates :status, inclusion: { in: REQUISITION_STATUSES }
  
  # Encrypted fields for sensitive data
  encrypts :access_token
  encrypts :refresh_token
  encrypts :account_data
  
  scope :active, -> { where(status: ['LN']) }
  scope :pending, -> { where(status: ['CR', 'GC', 'UA', 'SA', 'GA']) }
  scope :failed, -> { where(status: ['RJ', 'SU', 'EX']) }
  
  def linked?
    status == 'LN'
  end
  
  def pending?
    ['CR', 'GC', 'UA', 'SA', 'GA'].include?(status)
  end
  
  def failed?
    ['RJ', 'SU', 'EX'].include?(status)
  end
  
  def expired?
    status == 'EX' || (expires_at && expires_at < Time.current)
  end
  
  def institution_name
    institution_data&.dig('name') || institution_id
  end
  
  def account_ids
    return [] unless account_data.present?
    
    if account_data.is_a?(String)
      JSON.parse(account_data)
    else
      account_data
    end
  rescue JSON::ParserError
    []
  end
  
  def account_count
    account_ids.length
  end
  
  # Store account data as JSON
  def account_data=(data)
    if data.is_a?(Array) || data.is_a?(Hash)
      super(data.to_json)
    else
      super(data)
    end
  end
  
  def account_data
    return nil unless super.present?
    
    if super.is_a?(String)
      JSON.parse(super)
    else
      super
    end
  rescue JSON::ParserError
    nil
  end
  
  # Store institution data as JSON
  def institution_data=(data)
    if data.is_a?(Hash)
      super(data.to_json)
    else
      super(data)
    end
  end
  
  def institution_data
    return nil unless super.present?

    if super.is_a?(String)
      JSON.parse(super)
    else
      super
    end
  rescue JSON::ParserError
    nil
  end

  # Sync method required by Syncable concern
  def sync_data
    return unless linked?

    Rails.logger.info "Syncing GoCardless connection: #{requisition_id}"

    # Create or update the associated GocardlessItem
    item = find_or_create_gocardless_item

    # Sync the item which will sync all accounts and transactions
    item.sync_data

    Rails.logger.info "Completed syncing GoCardless connection: #{requisition_id}"
  rescue => error
    Rails.logger.error "Error syncing GoCardless connection #{requisition_id}: #{error.message}"
    raise error
  end

  private

  def find_or_create_gocardless_item
    return gocardless_item if gocardless_item.present?

    # Create GocardlessItem from connection data
    create_gocardless_item!(
      name: institution_name,
      requisition_id: requisition_id,
      access_token: access_token,
      refresh_token: refresh_token,
      status: :active,
      family: family,
      accountable_type: accountable_type || "Depository" # Default to Depository if not specified
    )
  end
end
