class GocardlessConnectionsController < ApplicationController
  layout "application"

  # before_action :authenticate_user! # Temporarily disabled for testing

  def index
    # For testing - use first family or create a test family
    family = Family.first || Family.create!(name: "Test Family")
    @connections = family.gocardless_connections.order(created_at: :desc)
  end

  def show
    # For testing - use first family
    family = Family.first || Family.create!(name: "Test Family")
    @connection = family.gocardless_connections.find(params[:id])

    # If connection is linked, fetch account data
    if @connection.linked? && @connection.access_token.present?
      fetch_account_data_for_connection(@connection)
    end
  end

  def new
    # Initialize the GoCardless service
    @service = GocardlessBankAccountDataService.new
    
    # Get access token
    token_response = @service.get_access_token
    unless token_response[:success]
      redirect_to gocardless_connections_path, alert: "Unable to connect to GoCardless: #{token_response[:error]}"
      return
    end
    
    @access_token = token_response[:access_token]
    
    # Get institutions from GoCardless
    institutions_response = @service.get_institutions(access_token: @access_token)

    if institutions_response[:success]
      @institutions = institutions_response[:institutions]
      Rails.logger.info "Loaded #{@institutions.length} institutions from GoCardless"
    else
      Rails.logger.error "Failed to load institutions: #{institutions_response[:error]}"
      @institutions = []
      flash.now[:alert] = "Unable to load bank list. Please try again later."
    end
    
    # Store access token in session for the form submission
    session[:gocardless_access_token] = @access_token
    session[:gocardless_token_expires_at] = Time.current + token_response[:expires_in].seconds
  end

  def create
    Rails.logger.info "Creating GoCardless connection with params: #{params.inspect}"

    # Get fresh access token instead of relying on session
    service = GocardlessBankAccountDataService.new
    token_response = service.get_access_token

    unless token_response[:success]
      redirect_to new_gocardless_connection_path, alert: "Unable to connect to GoCardless: #{token_response[:error]}"
      return
    end

    access_token = token_response[:access_token]

    institution_id = params[:institution_id]
    unless institution_id.present?
      redirect_to new_gocardless_connection_path, alert: "Please select a bank."
      return
    end
    
    # Create requisition
    requisition_response = service.create_requisition(
      institution_id: institution_id,
      redirect_url: gocardless_callback_url,
      access_token: access_token,
      reference: "maybe-#{family.id}-#{Time.current.to_i}"
    )
    
    unless requisition_response[:success]
      error_msg = "Failed to create bank connection: #{requisition_response[:error]}"
      
      Rails.logger.error error_msg
      Rails.logger.error "Error details: #{requisition_response[:details].inspect}" if requisition_response[:details]
      
      # Provide more helpful error message for localhost callback URL issue
      if requisition_response[:error]&.include?("400") && Rails.env.development?
        error_msg = "Development setup issue: GoCardless doesn't accept localhost URLs. Please set DEV_WEBHOOKS_URL environment variable to a public URL (e.g., using ngrok)."
      end
      
      redirect_to new_gocardless_connection_path, alert: error_msg
      return
    end
    
    requisition = requisition_response[:requisition]
    Rails.logger.info "Created requisition: #{requisition.inspect}"
    
    # Create connection record
    family = Family.first || Family.create!(name: "Test Family")
    @connection = family.gocardless_connections.build(
      requisition_id: requisition["id"],
      institution_id: institution_id,
      status: requisition["status"] || "CR",
      reference: requisition["reference"],
      access_token: access_token,
      token_expires_at: Time.current + 86400.seconds, # 24 hours
      institution_data: get_institution_data(institution_id, access_token)
    )
    
    if @connection.save
      Rails.logger.info "Saved GoCardless connection: #{@connection.inspect}"

      # Redirect to GoCardless consent page
      redirect_to requisition["link"], allow_other_host: true
    else
      Rails.logger.error "Failed to save GoCardless connection: #{@connection.errors.full_messages}"
      redirect_to new_gocardless_connection_path, alert: "Failed to save connection details."
    end
  end

  def destroy
    family = Family.first || Family.create!(name: "Test Family")
    @connection = family.gocardless_connections.find(params[:id])
    @connection.destroy!
    redirect_to gocardless_connections_path, notice: "Bank connection was successfully deleted."
  end

  def callback
    Rails.logger.info "GoCardless callback received with params: #{params.inspect}"
    
    requisition_id = params[:ref]
    
    unless requisition_id
      redirect_to gocardless_connections_path, alert: "Invalid callback - missing requisition reference."
      return
    end
    
    # Find the connection by requisition_id
    family = Family.first || Family.create!(name: "Test Family")
    @connection = family.gocardless_connections.find_by(requisition_id: requisition_id)
    
    unless @connection
      redirect_to gocardless_connections_path, alert: "Connection not found."
      return
    end
    
    # Get fresh access token
    service = GocardlessBankAccountDataService.new
    token_response = service.get_access_token
    
    unless token_response[:success]
      Rails.logger.error "Failed to get access token: #{token_response[:error]}"
      redirect_to gocardless_connections_path, alert: "Failed to verify connection status."
      return
    end
    
    access_token = token_response[:access_token]
    
    # Get requisition status from GoCardless
    requisition_response = service.get_requisition(
      requisition_id: requisition_id,
      access_token: access_token
    )
    
    unless requisition_response[:success]
      Rails.logger.error "Failed to get requisition status: #{requisition_response[:error]}"
      redirect_to gocardless_connections_path, alert: "Failed to verify connection status."
      return
    end
    
    requisition = requisition_response[:requisition]
    Rails.logger.info "Requisition status: #{requisition.inspect}"
    
    # Update the connection with the latest status and account data
    @connection.update!(
      status: requisition["status"],
      account_data: requisition["accounts"] || [],
      access_token: access_token,
      token_expires_at: Time.current + token_response[:expires_in].seconds,
      last_synced_at: Time.current
    )
    
    case requisition["status"]
    when "LN" # Linked
      redirect_to gocardless_connection_path(@connection), notice: "Bank account successfully connected!"
    when "RJ" # Rejected
      redirect_to gocardless_connections_path, alert: "Bank connection was rejected or cancelled."
    when "EX" # Expired
      redirect_to gocardless_connections_path, alert: "Bank connection link has expired."
    when "SU" # Suspended
      redirect_to gocardless_connections_path, alert: "Bank connection has been suspended."
    else
      # Still in progress (CR, GC, UA, SA, GA)
      redirect_to gocardless_connection_path(@connection), notice: "Bank connection is being processed..."
    end
  end

  private

    def gocardless_callback_url
      if Rails.env.development?
        # Use DEV_WEBHOOKS_URL if available (for ngrok or similar tunneling services)
        # Otherwise fall back to localhost (which may not work with GoCardless)
        base_url = ENV.fetch("DEV_WEBHOOKS_URL", "http://localhost:3000")
        "#{base_url}/gocardless_connections/callback"
      else
        url_for(controller: :gocardless_connections, action: :callback, only_path: false)
      end
    end
    
    def get_institution_data(institution_id, access_token)
      service = GocardlessBankAccountDataService.new
      
      if service.sandbox? && institution_id == service.sandbox_institution_id
        {
          "id" => service.sandbox_institution_id,
          "name" => "Sandbox Finance",
          "bic" => "SFIN0000",
          "countries" => ["GB"]
        }
      else
        institutions_response = service.get_institutions(access_token: access_token)
        if institutions_response[:success]
          institutions_response[:institutions].find { |inst| inst["id"] == institution_id }
        else
          nil
        end
      end
    end
    
    def fetch_account_data_for_connection(connection)
      return unless connection.access_token.present?
      
      service = GocardlessBankAccountDataService.new
      @accounts_data = []
      
      connection.account_ids.each do |account_id|
        # Get account details
        details_response = service.get_account_details(
          account_id: account_id,
          access_token: connection.access_token
        )
        
        # Get account balances
        balances_response = service.get_account_balances(
          account_id: account_id,
          access_token: connection.access_token
        )
        
        if details_response[:success] && balances_response[:success]
          @accounts_data << {
            id: account_id,
            details: details_response[:account_details],
            balances: balances_response[:balances]
          }
        else
          Rails.logger.error "Failed to fetch data for account #{account_id}"
        end
      end
    rescue => e
      Rails.logger.error "Error fetching account data: #{e.message}"
      @accounts_data = []
    end
end
