class GocardlessConnectionsController < ApplicationController
  layout "application"

  before_action :authenticate_user!
  protect_from_forgery with: :exception
  before_action :validate_family_access, except: [:callback]

  def index
    @connections = Current.family.gocardless_connections.order(created_at: :desc)
  end

  def show
    @connection = Current.family.gocardless_connections.find(params[:id])

    # If connection is linked, fetch account data
    if @connection.linked? && @connection.access_token.present?
      fetch_account_data_for_connection(@connection)

      # Trigger sync to pull transactions
      @connection.sync_later
    end
  rescue ActiveRecord::RecordNotFound
    redirect_to gocardless_connections_path, alert: "Connection not found."
  end

  def new
    begin
      # Initialize the GoCardless service
      @service = GocardlessBankAccountDataService.new

      # Get access token
      token_response = @service.get_access_token
      unless token_response[:success]
        Rails.logger.error "GoCardless token error: #{token_response[:error]}"
        redirect_to gocardless_connections_path, alert: "Unable to connect to GoCardless. Please try again later."
        return
      end

      @access_token = token_response[:access_token]

      # Get institutions from GoCardless
      institutions_response = @service.get_institutions(access_token: @access_token)

      if institutions_response[:success]
        @institutions = institutions_response[:institutions]
        Rails.logger.info "Loaded #{@institutions.length} institutions from GoCardless"

        if @institutions.empty?
          flash.now[:alert] = "No banks are currently available. Please try again later."
        end
      else
        Rails.logger.error "Failed to load institutions: #{institutions_response[:error]}"
        @institutions = []
        flash.now[:alert] = "Unable to load bank list. Please check your internet connection and try again."
      end

      # Store access token in session for the form submission
      session[:gocardless_access_token] = @access_token
      session[:gocardless_token_expires_at] = Time.current + token_response[:expires_in].seconds

    rescue StandardError => e
      Rails.logger.error "Unexpected error in GoCardless new action: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      redirect_to gocardless_connections_path, alert: "An unexpected error occurred. Please try again later."
    end
  end

  def create
    begin
      Rails.logger.info "Creating GoCardless connection with params: #{params.inspect}"

      # Validate institution_id parameter
      institution_id = params[:institution_id]
      if institution_id.blank?
        redirect_to new_gocardless_connection_path, alert: "Please select a bank."
        return
      end

      # Validate institution_id format (should be alphanumeric with underscores)
      unless institution_id.match?(/\A[A-Z0-9_]+\z/)
        Rails.logger.warn "Invalid institution_id format: #{institution_id}"
        redirect_to new_gocardless_connection_path, alert: "Invalid bank selection. Please try again."
        return
      end

      # Get fresh access token instead of relying on session
      service = GocardlessBankAccountDataService.new
      token_response = service.get_access_token

      unless token_response[:success]
        Rails.logger.error "GoCardless token error during create: #{token_response[:error]}"
        redirect_to new_gocardless_connection_path, alert: "Unable to connect to GoCardless. Please try again."
        return
      end

      access_token = token_response[:access_token]
    
      # Create requisition
      requisition_response = service.create_requisition(
        institution_id: institution_id,
        redirect_url: gocardless_callback_url,
        access_token: access_token,
        reference: "maybe-#{Current.family.id}-#{Time.current.to_i}"
      )

      unless requisition_response[:success]
        error_msg = "Failed to create bank connection: #{requisition_response[:error]}"

        Rails.logger.error error_msg
        Rails.logger.error "Error details: #{requisition_response[:details].inspect}" if requisition_response[:details]

        # Provide more helpful error messages based on error type
        if requisition_response[:error]&.include?("400") && Rails.env.development?
          error_msg = "Development setup issue: GoCardless doesn't accept localhost URLs. Please set DEV_WEBHOOKS_URL environment variable to a public URL (e.g., using ngrok)."
        elsif requisition_response[:error]&.include?("institution")
          error_msg = "The selected bank is currently unavailable. Please try a different bank."
        elsif requisition_response[:error]&.include?("timeout")
          error_msg = "Connection timeout. Please check your internet connection and try again."
        end

        redirect_to new_gocardless_connection_path, alert: error_msg
        return
      end
    
      requisition = requisition_response[:requisition]
      Rails.logger.info "Created requisition: #{requisition.inspect}"

      # Validate requisition response
      unless requisition&.dig("id") && requisition&.dig("link")
        Rails.logger.error "Invalid requisition response: missing id or link"
        redirect_to new_gocardless_connection_path, alert: "Invalid response from GoCardless. Please try again."
        return
      end

      # Create connection record
      @connection = Current.family.gocardless_connections.build(
        requisition_id: requisition["id"],
        institution_id: institution_id,
        status: requisition["status"] || "CR",
        reference: requisition["reference"],
        access_token: access_token,
        token_expires_at: Time.current + 86400.seconds, # 24 hours
        institution_data: get_institution_data(institution_id, access_token),
        accountable_type: params[:accountable_type] # Store the account type for later use
      )

      if @connection.save
        Rails.logger.info "Saved GoCardless connection: #{@connection.inspect}"

        # Redirect to GoCardless consent page
        redirect_to requisition["link"], allow_other_host: true
      else
        Rails.logger.error "Failed to save GoCardless connection: #{@connection.errors.full_messages}"
        redirect_to new_gocardless_connection_path, alert: "Failed to save connection details: #{@connection.errors.full_messages.join(', ')}"
      end

    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Database validation error: #{e.message}"
      redirect_to new_gocardless_connection_path, alert: "Failed to create connection. Please try again."
    rescue StandardError => e
      Rails.logger.error "Unexpected error in GoCardless create action: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      redirect_to new_gocardless_connection_path, alert: "An unexpected error occurred. Please try again later."
    end
  end

  def destroy
    @connection = Current.family.gocardless_connections.find(params[:id])
    @connection.destroy!
    redirect_to gocardless_connections_path, notice: "Bank connection was successfully deleted."
  rescue ActiveRecord::RecordNotFound
    redirect_to gocardless_connections_path, alert: "Connection not found."
  end

  def sync
    @connection = Current.family.gocardless_connections.find(params[:id])

    unless @connection.linked?
      redirect_to gocardless_connection_path(@connection), alert: "Cannot sync - connection is not linked."
      return
    end

    @connection.sync_later
    redirect_to gocardless_connection_path(@connection), notice: "Transaction sync started. This may take a few moments."
  rescue ActiveRecord::RecordNotFound
    redirect_to gocardless_connections_path, alert: "Connection not found."
  end

  def callback
    Rails.logger.info "GoCardless callback received with params: #{params.inspect}"
    
    requisition_id = params[:ref]
    
    unless requisition_id
      redirect_to gocardless_connections_path, alert: "Invalid callback - missing requisition reference."
      return
    end
    
    # Find the connection by requisition_id
    @connection = Current.family.gocardless_connections.find_by(requisition_id: requisition_id)
    
    unless @connection
      redirect_to gocardless_connections_path, alert: "Connection not found."
      return
    end
    
    # Get fresh access token
    service = GocardlessBankAccountDataService.new
    token_response = service.get_access_token
    
    unless token_response[:success]
      Rails.logger.error "Failed to get access token: #{token_response[:error]}"
      redirect_to gocardless_connections_path, alert: "Failed to verify connection status."
      return
    end
    
    access_token = token_response[:access_token]
    
    # Get requisition status from GoCardless
    requisition_response = service.get_requisition(
      requisition_id: requisition_id,
      access_token: access_token
    )
    
    unless requisition_response[:success]
      Rails.logger.error "Failed to get requisition status: #{requisition_response[:error]}"
      redirect_to gocardless_connections_path, alert: "Failed to verify connection status."
      return
    end
    
    requisition = requisition_response[:requisition]
    Rails.logger.info "Requisition status: #{requisition.inspect}"
    
    # Update the connection with the latest status and account data
    @connection.update!(
      status: requisition["status"],
      account_data: requisition["accounts"] || [],
      access_token: access_token,
      token_expires_at: Time.current + token_response[:expires_in].seconds,
      last_synced_at: Time.current
    )
    
    case requisition["status"]
    when "LN" # Linked
      # Trigger sync to pull accounts and transactions
      @connection.sync_later
      redirect_to accounts_path, notice: "🎉 Bank account successfully connected! Your transactions are being imported and will appear shortly."
    when "RJ" # Rejected
      redirect_to new_gocardless_connection_path(accountable_type: @connection.accountable_type), alert: "Bank connection was rejected or cancelled. Please try again."
    when "EX" # Expired
      redirect_to new_gocardless_connection_path(accountable_type: @connection.accountable_type), alert: "Bank connection link has expired. Please try again."
    when "SU" # Suspended
      redirect_to gocardless_connections_path, alert: "Bank connection has been suspended."
    else
      # Still in progress (CR, GC, UA, SA, GA)
      redirect_to gocardless_connection_path(@connection), notice: "Bank connection is being processed. This page will update automatically."
    end
  end

  private

    def validate_family_access
      unless Current.family
        Rails.logger.warn "No family found for user #{current_user&.id}"
        redirect_to root_path, alert: "Access denied. Please ensure you have a valid family account."
        return false
      end
      true
    end

    def gocardless_callback_url
      if Rails.env.development?
        # Use DEV_WEBHOOKS_URL if available (for ngrok or similar tunneling services)
        # Otherwise fall back to localhost (which may not work with GoCardless)
        base_url = ENV.fetch("DEV_WEBHOOKS_URL", "http://localhost:3000")
        "#{base_url}/gocardless_connections/callback"
      else
        url_for(controller: :gocardless_connections, action: :callback, only_path: false)
      end
    end
    
    def get_institution_data(institution_id, access_token)
      service = GocardlessBankAccountDataService.new
      
      if service.sandbox? && institution_id == service.sandbox_institution_id
        {
          "id" => service.sandbox_institution_id,
          "name" => "Sandbox Finance",
          "bic" => "SFIN0000",
          "countries" => ["GB"]
        }
      else
        institutions_response = service.get_institutions(access_token: access_token)
        if institutions_response[:success]
          institutions_response[:institutions].find { |inst| inst["id"] == institution_id }
        else
          nil
        end
      end
    end
    
    def fetch_account_data_for_connection(connection)
      return unless connection.access_token.present?
      
      service = GocardlessBankAccountDataService.new
      @accounts_data = []
      
      connection.account_ids.each do |account_id|
        # Get account details
        details_response = service.get_account_details(
          account_id: account_id,
          access_token: connection.access_token
        )
        
        # Get account balances
        balances_response = service.get_account_balances(
          account_id: account_id,
          access_token: connection.access_token
        )
        
        if details_response[:success] && balances_response[:success]
          @accounts_data << {
            id: account_id,
            details: details_response[:account_details],
            balances: balances_response[:balances]
          }
        else
          Rails.logger.error "Failed to fetch data for account #{account_id}"
        end
      end
    rescue => e
      Rails.logger.error "Error fetching account data: #{e.message}"
      @accounts_data = []
    end
end
