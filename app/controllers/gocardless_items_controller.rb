class GocardlessItemsController < ApplicationController
  before_action :set_gocardless_item, only: %i[show destroy sync]

  def index
    @gocardless_items = Current.family.gocardless_items.ordered.includes(:accounts)
  end

  def show
    @accounts = @gocardless_item.accounts.alphabetically
  end

  def new
    @provider = Provider::Registry.get_provider(:gocardless)
    @institutions = fetch_institutions
  end

  def create
    Rails.logger.info "=== GoCardless Create Action Started ==="
    Rails.logger.info "Institution ID: #{params[:institution_id]}"
    Rails.logger.info "Family ID: #{Current.family&.id}"

    if params[:institution_id].blank?
      Rails.logger.warn "No institution ID provided"
      redirect_to new_gocardless_item_path, alert: "Please select a bank"
      return
    end

    begin
      Rails.logger.info "Creating GoCardless agreement..."

      # Create agreement for the selected institution
      agreement_response = provider.create_agreement(
        institution_id: params[:institution_id],
        max_historical_days: 90,
        access_valid_for_days: 90,
        access_scope: ["balances", "details", "transactions"]
      )

      Rails.logger.info "Agreement response success: #{agreement_response.success?}"
      Rails.logger.info "Agreement response data: #{agreement_response.data.inspect}"

      if agreement_response.error
        Rails.logger.error "Agreement error: #{agreement_response.error.inspect}"
      end

      unless agreement_response.success?
        error_msg = "Failed to create agreement: #{agreement_response.error&.message}"
        Rails.logger.error error_msg
        redirect_to new_gocardless_item_path, alert: error_msg
        return
      end

      agreement_id = agreement_response.data["id"]
      Rails.logger.info "Agreement created successfully with ID: #{agreement_id}"

      # Create requisition (connection request)
      Rails.logger.info "Creating GoCardless requisition..."
      Rails.logger.info "Callback URL: #{gocardless_callback_url}"

      requisition_response = provider.create_requisition(
        institution_id: params[:institution_id],
        redirect_url: gocardless_callback_url,
        reference: "maybe-#{Current.family.id}",
        agreement_id: agreement_id
      )

      Rails.logger.info "Requisition response success: #{requisition_response.success?}"
      Rails.logger.info "Requisition response data: #{requisition_response.data.inspect}"

      if requisition_response.error
        Rails.logger.error "Requisition error: #{requisition_response.error.inspect}"
      end

      unless requisition_response.success?
        error_msg = "Failed to create requisition: #{requisition_response.error&.message}"
        error_details = requisition_response.error&.details

        Rails.logger.error error_msg
        Rails.logger.error "Error details: #{error_details.inspect}" if error_details

        # Provide more helpful error message for localhost callback URL issue
        if error_msg.include?("400") && Rails.env.development?
          error_msg = "Development setup issue: GoCardless doesn't accept localhost URLs. Please set DEV_WEBHOOKS_URL environment variable to a public URL (e.g., using ngrok)."
        end

        redirect_to new_gocardless_item_path, alert: error_msg
        return
      end

      requisition_data = requisition_response.data
      link_url = requisition_data["link"]

      Rails.logger.info "Requisition created successfully"
      Rails.logger.info "Requisition ID: #{requisition_data['id']}"
      Rails.logger.info "GoCardless link URL: #{link_url}"

      # Store requisition info for callback
      session[:gocardless_requisition_id] = requisition_data["id"]
      session[:gocardless_institution_id] = params[:institution_id]

      Rails.logger.info "Redirecting to GoCardless authentication URL"
      # Redirect user to GoCardless for bank authentication
      redirect_to link_url, allow_other_host: true

    rescue => error
      Rails.logger.error "GoCardless connection error: #{error.class}: #{error.message}"
      Rails.logger.error "Backtrace: #{error.backtrace.first(10).join("\n")}"
      redirect_to new_gocardless_item_path, alert: "Failed to connect to bank. Please try again."
    end
  end

  def callback
    requisition_id = session[:gocardless_requisition_id]
    institution_id = session[:gocardless_institution_id]

    unless requisition_id.present? && institution_id.present?
      redirect_to accounts_path, alert: "Invalid callback. Please try connecting again."
      return
    end

    begin
      # Fetch requisition to check status
      requisition_response = provider.get_requisition(requisition_id: requisition_id)

      unless requisition_response.success?
        redirect_to accounts_path, alert: "Failed to verify bank connection."
        return
      end

      requisition_data = requisition_response.data
      
      if requisition_data["status"] != "LN" # LN = Linked
        redirect_to accounts_path, alert: "Bank connection was not completed. Please try again."
        return
      end

      # Get institution name
      institution_name = get_institution_name(institution_id)

      # Create GoCardless item
      gocardless_item = Current.family.gocardless_items.create!(
        name: institution_name,
        requisition_id: requisition_id,
        institution_id: institution_id,
        status: :active
      )

      # Clear session
      session.delete(:gocardless_requisition_id)
      session.delete(:gocardless_institution_id)

      # Sync accounts in background
      gocardless_item.sync_later

      redirect_to gocardless_item_path(gocardless_item), notice: "Successfully connected to #{institution_name}!"

    rescue => error
      Rails.logger.error "GoCardless callback error: #{error.message}"
      redirect_to accounts_path, alert: "Failed to complete bank connection."
    end
  end

  def destroy
    @gocardless_item.destroy_later
    redirect_to gocardless_items_path, notice: "Bank connection removed successfully"
  end

  def sync
    @gocardless_item.sync_later
    redirect_to gocardless_item_path(@gocardless_item), notice: "Account sync started"
  end

  private
    def set_gocardless_item
      @gocardless_item = Current.family.gocardless_items.find(params[:id])
    end

    def provider
      @provider ||= Provider::Registry.get_provider(:gocardless)
    end

    def fetch_institutions
      return [] unless provider.present?

      response = provider.list_institutions(country: "GB")

      institutions = if response.success?
        response.data.map do |institution|
          {
            id: institution["id"],
            name: institution["name"],
            bic: institution["bic"],
            logo: institution["logo"],
            countries: institution["countries"]
          }
        end
      else
        Rails.logger.error "Failed to fetch institutions: #{response.error&.message}"
        []
      end

      # In sandbox environment, highlight a real institution for testing
      # We'll use ABN AMRO Bank Commercial as it's commonly available for testing
      if Rails.env.development? || Rails.application.config.gocardless&.dig(:environment) == "sandbox"
        # Find ABN AMRO or another suitable test institution
        test_institution = institutions.find { |inst| inst[:id] == "ABNAMRO_ABNAGB2LXXX" }
        if test_institution
          # Move it to the top and mark it as recommended for testing
          institutions.delete(test_institution)
          test_institution[:name] = "#{test_institution[:name]} (Recommended for Testing)"
          institutions.unshift(test_institution)
        end
      end

      institutions
    rescue => error
      Rails.logger.error "Error fetching institutions: #{error.message}"
      []
    end

    def get_institution_name(institution_id)
      institutions = fetch_institutions
      institution = institutions.find { |i| i[:id] == institution_id }
      institution&.dig(:name) || "GoCardless Bank"
    end

    def gocardless_callback_url
      if Rails.env.development?
        # Use DEV_WEBHOOKS_URL if available (for ngrok or similar tunneling services)
        # Otherwise fall back to localhost (which may not work with GoCardless)
        base_url = ENV.fetch("DEV_WEBHOOKS_URL", "http://localhost:3000")
        "#{base_url}/gocardless_items/callback"
      else
        url_for(controller: :gocardless_items, action: :callback, only_path: false)
      end
    end

    # Make provider available to views
    helper_method :provider
end 