class Admin::GocardlessProvidersController < ApplicationController
  before_action :require_admin

  def index
    @provider = Provider::Registry.get_provider(:gocardless)
    @provider_status = get_provider_status
  end

  def switch_provider
    provider = Provider::Registry.get_provider(:gocardless)
    
    if provider.is_a?(Provider::GocardlessManager)
      if provider.switch_to_next_provider
        flash[:notice] = "Successfully switched to next GoCardless provider"
      else
        flash[:alert] = "Failed to switch providers (only one provider available)"
      end
    else
      flash[:alert] = "Provider switching not available (backup not configured)"
    end
    
    redirect_to admin_gocardless_providers_path
  end

  def reset_to_primary
    provider = Provider::Registry.get_provider(:gocardless)
    
    if provider.is_a?(Provider::GocardlessManager)
      provider.reset_to_primary_provider
      flash[:notice] = "Reset to primary GoCardless provider"
    else
      flash[:alert] = "Provider reset not available (backup not configured)"
    end
    
    redirect_to admin_gocardless_providers_path
  end

  def test_connection
    provider = Provider::Registry.get_provider(:gocardless)
    
    begin
      response = provider.get_access_token
      
      if response.success?
        flash[:notice] = "GoCardless connection test successful"
      else
        flash[:alert] = "GoCardless connection test failed: #{response.error&.message}"
      end
    rescue => e
      flash[:alert] = "GoCardless connection test error: #{e.message}"
    end
    
    redirect_to admin_gocardless_providers_path
  end

  private

  def require_admin
    # Add your admin authentication logic here
    # For now, just check if user is present
    redirect_to root_path unless current_user.present?
  end

  def get_provider_status
    provider = Provider::Registry.get_provider(:gocardless)
    
    base_status = {
      provider_class: provider.class.name,
      configured: provider.present?,
      healthy: provider&.healthy? || false
    }

    if provider.is_a?(Provider::GocardlessManager)
      base_status.merge!(provider.provider_status)
      base_status[:backup_available] = true
      base_status[:failover_enabled] = true
    else
      base_status[:backup_available] = false
      base_status[:failover_enabled] = false
      base_status[:total_providers] = 1
      base_status[:available_providers] = provider.present? ? 1 : 0
      base_status[:current_provider] = 1
    end

    base_status
  end
end
