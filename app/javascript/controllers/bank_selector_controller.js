import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["submit"]

  connect() {
    console.log("Bank selector controller connected")
    this.updateSubmitButton()
    this.setupFormSubmission()
  }

  setupFormSubmission() {
    // Find the form element
    const form = this.element.querySelector('form') || this.element
    if (form) {
      console.log("Setting up form submission for:", form.action)
      form.addEventListener('submit', this.handleSubmit.bind(this))
    } else {
      console.error("No form found in bank selector")
    }
  }

  handleSubmit(event) {
    console.log("Form submit intercepted")
    event.preventDefault()

    const selectedBank = this.element.querySelector('input[name="institution_id"]:checked')
    if (!selectedBank) {
      alert('Please select a bank before continuing.')
      return false
    }

    console.log('Submitting form with bank:', selectedBank.value)

    // Disable submit button to prevent double submission
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = true
      this.submitTarget.value = "Connecting..."
    }

    // Get form data
    const form = event.target
    const formData = new FormData(form)

    // Log form data for debugging
    console.log('Form data being submitted:')
    for (let [key, value] of formData.entries()) {
      console.log(`- ${key}: ${value}`)
    }

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                      document.querySelector('input[name="authenticity_token"]')?.value

    // Submit the form using fetch
    fetch(form.action, {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'X-CSRF-Token': csrfToken
      },
      credentials: 'same-origin'
    })
    .then(response => {
      console.log('Response status:', response.status)
      console.log('Response URL:', response.url)

      if (response.ok) {
        // Check if this is a redirect response
        if (response.redirected || response.url !== form.action) {
          console.log('Redirecting to:', response.url)
          window.location.href = response.url
        } else {
          // Handle HTML response
          return response.text().then(html => {
            console.log('Response HTML length:', html.length)

            // Check if response contains a redirect
            if (html.includes('gocardless.com') || html.includes('redirect')) {
              console.log('GoCardless redirect detected in response')
              // Try to extract redirect URL from response
              const parser = new DOMParser()
              const doc = parser.parseFromString(html, 'text/html')
              const redirectUrl = doc.querySelector('meta[http-equiv="refresh"]')?.getAttribute('content')?.split('url=')[1]
              if (redirectUrl) {
                console.log('Following redirect to:', redirectUrl)
                window.location.href = redirectUrl
              } else {
                // Look for JavaScript redirect
                const scriptTags = doc.querySelectorAll('script')
                for (let script of scriptTags) {
                  if (script.textContent.includes('window.location')) {
                    console.log('JavaScript redirect found')
                    eval(script.textContent)
                    return
                  }
                }
                // If no redirect found, replace page content
                document.documentElement.innerHTML = html
              }
            } else {
              // Replace current page content
              console.log('Replacing page content')
              document.documentElement.innerHTML = html
            }
          })
        }
      } else {
        console.error('Form submission failed with status:', response.status)
        this.handleSubmissionError('Server error occurred. Please try again.')
      }
    })
    .catch(error => {
      console.error('Form submission error:', error)
      this.handleSubmissionError('Network error occurred. Please try again.')
    })

    return false
  }

  handleSubmissionError(message) {
    alert(message)

    // Re-enable submit button
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = false
      this.submitTarget.value = "Connect Bank Account"
    }
  }

  bankSelected(event) {
    console.log("Bank selection changed")
    this.updateSubmitButton()
  }

  updateSubmitButton() {
    const selectedBank = this.element.querySelector('input[name="institution_id"]:checked')

    if (this.hasSubmitTarget) {
      if (selectedBank) {
        this.submitTarget.disabled = false
        this.submitTarget.classList.remove("opacity-50", "cursor-not-allowed")
        this.submitTarget.classList.add("cursor-pointer")
        console.log("Submit button enabled for bank:", selectedBank.value)
      } else {
        this.submitTarget.disabled = true
        this.submitTarget.classList.add("opacity-50", "cursor-not-allowed")
        this.submitTarget.classList.remove("cursor-pointer")
        console.log("Submit button disabled - no bank selected")
      }
    } else {
      console.warn("No submit button target found")
    }
  }
}