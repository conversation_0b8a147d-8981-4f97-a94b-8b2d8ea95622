import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["option", "radio", "submit"]

  connect() {
    console.log("Bank selector controller connected")
    console.log("Option targets:", this.optionTargets.length)
    console.log("Radio targets:", this.radioTargets.length) 
    console.log("Submit button target:", this.hasSubmitTarget)
    this.updateSubmitButton()
  }

  selectBank(event) {
    // Prevent event bubbling to avoid conflicts
    event.stopPropagation()

    // Don't handle if the click was directly on the radio button
    if (event.target.type === 'radio') {
      return
    }

    console.log("Bank selected:", event.currentTarget)

    const clickedOption = event.currentTarget
    const radio = clickedOption.querySelector('input[type="radio"]')

    if (radio && !radio.checked) {
      // Set the radio button as checked
      radio.checked = true
      console.log("Radio checked:", radio.checked)

      // Trigger the radio change event manually
      radio.dispatchEvent(new Event('change', { bubbles: true }))
    }
  }

  updateSubmitButton() {
    if (!this.hasSubmitTarget) {
      console.log("No submit button target found")
      return
    }

    const anySelected = this.radioTargets.some(radio => radio.checked)
    console.log("Any radio selected:", anySelected)

    if (anySelected) {
      this.submitTarget.disabled = false
      this.submitTarget.classList.remove("opacity-50", "cursor-not-allowed")
      this.submitTarget.classList.add("cursor-pointer")
    } else {
      this.submitTarget.disabled = true
      this.submitTarget.classList.add("opacity-50", "cursor-not-allowed")
      this.submitTarget.classList.remove("cursor-pointer")
    }
  }

  // Handle direct radio button clicks
  radioChanged(event) {
    console.log("Radio changed:", event.target)

    // Find the parent option element and update visual state
    const radio = event.target
    const parentOption = radio.closest('[data-bank-selector-target="option"]')

    if (parentOption && radio.checked) {
      this.updateVisualSelection(parentOption)
    }

    this.updateSubmitButton()
  }

  updateVisualSelection(selectedOption) {
    // Remove selection from all options
    this.optionTargets.forEach(option => {
      option.classList.remove("border-primary", "bg-surface-hover")
      option.classList.add("border-gray-200")

      // Hide checkmarks on all options
      const checkmark = option.querySelector(".selected-checkmark")
      if (checkmark) checkmark.classList.add("opacity-0")
    })

    // Add selection to selected option
    selectedOption.classList.remove("border-gray-200")
    selectedOption.classList.add("border-primary", "bg-surface-hover")

    // Show checkmark on selected option
    const checkmark = selectedOption.querySelector(".selected-checkmark")
    if (checkmark) checkmark.classList.remove("opacity-0")
  }
} 