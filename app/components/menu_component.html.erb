<%= tag.div data: { controller: "menu", menu_placement_value: placement, menu_offset_value: offset, testid: testid } do %>
  <% if variant == :icon %>
    <%= render ButtonComponent.new(variant: "icon", icon: icon_vertical ? "more-vertical" : "more-horizontal", data: { menu_target: "button" }) %>
  <% elsif variant == :button %>
    <%= button %>
  <% elsif variant == :avatar %>
    <button data-menu-target="button">
      <div class="w-9 h-9 cursor-pointer">
        <%= render "settings/user_avatar", avatar_url: avatar_url, initials: initials %>
      </div>
    </button>
  <% end %>

  <div data-menu-target="content" class="px-2 lg:px-0 max-w-full hidden z-50">
    <div class="mx-auto min-w-[200px] shadow-border-xs bg-container rounded-lg">
      <%= header %>

      <%= tag.div class: class_names("py-1" => !no_padding) do %>
        <% items.each do |item| %>
          <%= item %>
        <% end %>

        <%= custom_content %>
      <% end %>
    </div>
  </div>
<% end %>
