<% if params[:redirect_uri]&.start_with?('maybeapp://') || params[:display] == 'mobile' %>
  <meta name="turbo-visit-control" content="reload">
<% end %>

<div class="bg-container rounded-xl p-6 space-y-6">
  <div class="space-y-2 text-center">
    <p class="text-sm text-secondary">
      <%= raw t(".prompt", client_name: content_tag(:span, @pre_auth.client.name, class: "font-medium text-primary")) %>
    </p>
  </div>

  <% if @pre_auth.scopes.count > 0 %>
    <div class="bg-surface-inset rounded-lg p-4 space-y-3">
      <p class="text-sm font-medium text-primary"><%= t(".able_to") %>:</p>
      <ul class="space-y-2">
        <% @pre_auth.scopes.each do |scope| %>
          <li class="flex items-start gap-2 text-sm text-secondary">
            <%= icon("check", class: "w-4 h-4 mt-0.5 text-success") %>
            <span><%= t scope, scope: [:doorkeeper, :scopes] %></span>
          </li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="space-y-3">
    <% turbo_disabled = params[:redirect_uri]&.start_with?("maybeapp://") || params[:display] == "mobile" %>
    <%= form_tag oauth_authorization_path, method: :post, class: "w-full", data: { turbo: !turbo_disabled } do %>
      <%= hidden_field_tag :client_id, @pre_auth.client.uid, id: nil %>
      <%= hidden_field_tag :redirect_uri, @pre_auth.redirect_uri, id: nil %>
      <%= hidden_field_tag :state, @pre_auth.state, id: nil %>
      <%= hidden_field_tag :response_type, @pre_auth.response_type, id: nil %>
      <%= hidden_field_tag :response_mode, @pre_auth.response_mode, id: nil %>
      <%= hidden_field_tag :scope, @pre_auth.scope, id: nil %>
      <%= hidden_field_tag :code_challenge, @pre_auth.code_challenge, id: nil %>
      <%= hidden_field_tag :code_challenge_method, @pre_auth.code_challenge_method, id: nil %>
      <% if params[:display].present? %>
        <%= hidden_field_tag :display, params[:display], id: nil %>
      <% end %>
      <%= render ButtonComponent.new(
        text: t("doorkeeper.authorizations.buttons.authorize"),
        variant: :primary,
        size: :lg,
        full_width: true,
        href: oauth_authorization_path,
        data: { disable_with: "Authorizing..." }
      ) %>
    <% end %>

    <%= form_tag oauth_authorization_path, method: :delete, class: "w-full", data: { turbo: !turbo_disabled } do %>
      <%= hidden_field_tag :client_id, @pre_auth.client.uid, id: nil %>
      <%= hidden_field_tag :redirect_uri, @pre_auth.redirect_uri, id: nil %>
      <%= hidden_field_tag :state, @pre_auth.state, id: nil %>
      <%= hidden_field_tag :response_type, @pre_auth.response_type, id: nil %>
      <%= hidden_field_tag :response_mode, @pre_auth.response_mode, id: nil %>
      <%= hidden_field_tag :scope, @pre_auth.scope, id: nil %>
      <%= hidden_field_tag :code_challenge, @pre_auth.code_challenge, id: nil %>
      <%= hidden_field_tag :code_challenge_method, @pre_auth.code_challenge_method, id: nil %>
      <% if params[:display].present? %>
        <%= hidden_field_tag :display, params[:display], id: nil %>
      <% end %>
      <%= render ButtonComponent.new(
        text: t("doorkeeper.authorizations.buttons.deny"),
        variant: :outline,
        size: :lg,
        full_width: true,
        href: oauth_authorization_path,
        data: { disable_with: "Denying..." }
      ) %>
    <% end %>
  </div>

  <p class="text-xs text-tertiary text-center">
    By authorizing, you allow this app to access your Maybe data according to the permissions above.
  </p>
</div>
