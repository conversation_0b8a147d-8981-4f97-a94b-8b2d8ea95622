<%# locals: (budget:) %>

<div>
  <div class="p-4 border-b border-secondary">
    <h3 class="text-sm text-secondary mb-2">Expected income</h3>

    <span class="inline-block mb-2 text-xl font-medium text-primary">
      <%= format_money(budget.expected_income_money) %>
    </span>

    <div>
      <div class="flex h-1.5 mb-3 gap-1">
        <% if budget.remaining_expected_income.negative? %>
          <div class="rounded-md h-1.5 bg-green-500" style="width: <%= 100 - budget.surplus_percent %>%"></div>
          <div class="rounded-md h-1.5 bg-green-500" style="width: <%= budget.surplus_percent %>%"></div>
        <% else %>
          <div class="rounded-md h-1.5 bg-green-500" style="width: <%= budget.actual_income_percent %>%"></div>
          <div class="rounded-md h-1.5 bg-surface-inset" style="width: <%= 100 - budget.actual_income_percent %>%"></div>
        <% end %>
      </div>
      <div class="flex justify-between text-sm">
        <p class="text-secondary"><%= format_money(budget.actual_income_money) %> earned</p>
        <p class="font-medium">
          <% if budget.remaining_expected_income.negative? %>
            <span class="text-green-500"><%= format_money(budget.remaining_expected_income_money.abs) %> over</span>
          <% else %>
            <span class="text-primary"><%= format_money(budget.remaining_expected_income_money) %> left</span>
          <% end %>
        </p>
      </div>
    </div>
  </div>

  <div class="p-4">
    <h3 class="text-sm text-secondary mb-2">Budgeted</h3>

    <span class="inline-block mb-2 text-xl font-medium text-primary">
      <%= format_money(budget.budgeted_spending_money) %>
    </span>

    <div>
      <div class="flex h-1.5 mb-3 gap-1">
        <% if budget.available_to_spend.negative? %>
          <div class="rounded-md h-1.5 bg-inverse" style="width: <%= 100 - budget.overage_percent %>%"></div>
          <div class="rounded-md h-1.5 bg-destructive" style="width: <%= budget.overage_percent %>%"></div>
        <% else %>
          <div class="rounded-md h-1.5 bg-inverse" style="width: <%= budget.percent_of_budget_spent %>%"></div>
          <div class="rounded-md h-1.5 bg-surface-inset" style="width: <%= 100 - budget.percent_of_budget_spent %>%"></div>
        <% end %>
      </div>
      <div class="flex justify-between text-sm">
        <p class="text-secondary"><%= format_money(budget.actual_spending_money) %> spent</p>
        <p class="font-medium">
          <% if budget.available_to_spend.negative? %>
            <span class="text-destructive"><%= format_money(budget.available_to_spend_money.abs) %> over</span>
          <% else %>
            <span class="text-primary"><%= format_money(budget.available_to_spend_money) %> left</span>
          <% end %>
        </p>
      </div>
    </div>
  </div>
</div>
