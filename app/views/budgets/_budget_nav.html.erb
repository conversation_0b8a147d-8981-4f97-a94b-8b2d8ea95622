<%# locals: (budget:) %>

<% steps = [
  { name: "Setup", path: edit_budget_path(budget), is_complete: budget.initialized?, step_number: 1 },
  { name: "Categories", path: budget_budget_categories_path(budget), is_complete: budget.allocations_valid?, step_number: 2 },
] %>

<ul class="flex items-center gap-2">
  <% steps.each_with_index do |step, idx| %>
    <li class="flex items-center gap-2 group">
      <% is_current = request.path == step[:path] %>

      <% text_class = if is_current
                  "text-primary"
                else
                  step[:is_complete] ? "text-green-600" : "text-secondary"
                end %>
      <% step_class = if is_current
                  "bg-primary text-primary"
                else
                  step[:is_complete] ? "bg-green-600/10 border-alpha-black-25" : "bg-container-inset"
                end %>

      <%= link_to step[:path], class: "flex items-center gap-3" do %>
        <div class="flex items-center gap-2 text-sm font-medium <%= text_class %>">
          <span class="<%= step_class %> w-7 h-7 rounded-full shrink-0 inline-flex items-center justify-center border border-transparent">
            <%= step[:is_complete] && !is_current ? icon("check", size: "sm") : idx + 1 %>
          </span>

          <span><%= step[:name] %></span>
        </div>
      <% end %>

      <hr class="border border-secondary w-12 group-last:hidden">
    </li>
  <% end %>
</ul>
