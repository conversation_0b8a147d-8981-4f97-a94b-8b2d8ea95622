<% content_for :title, "Bank Connections" %>

<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Bank Connections</h1>
      <p class="mt-1 text-sm text-gray-600">
        Manage your connected bank accounts and view their status.
      </p>
    </div>
    
    <%= link_to new_gocardless_connection_path, 
        class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" do %>
      <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
      Connect Bank Account
    <% end %>
  </div>

  <% if @connections.any? %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
      <div class="divide-y divide-gray-200">
        <% @connections.each do |connection| %>
          <div class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <% case connection.status %>
                  <% when "LN" %>
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  <% when "CR", "GC", "UA", "SA", "GA" %>
                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  <% else %>
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  <% end %>
                </div>
                
                <div class="ml-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    <%= connection.institution_name %>
                  </h3>
                  <div class="flex items-center mt-1">
                    <span class="text-sm text-gray-500">
                      Status: 
                      <% case connection.status %>
                      <% when "LN" %>
                        <span class="text-green-600 font-medium">Connected</span>
                      <% when "CR" %>
                        <span class="text-yellow-600 font-medium">Created</span>
                      <% when "GC" %>
                        <span class="text-yellow-600 font-medium">Giving Consent</span>
                      <% when "UA" %>
                        <span class="text-yellow-600 font-medium">Authenticating</span>
                      <% when "SA" %>
                        <span class="text-yellow-600 font-medium">Selecting Accounts</span>
                      <% when "GA" %>
                        <span class="text-yellow-600 font-medium">Granting Access</span>
                      <% when "RJ" %>
                        <span class="text-red-600 font-medium">Rejected</span>
                      <% when "SU" %>
                        <span class="text-red-600 font-medium">Suspended</span>
                      <% when "EX" %>
                        <span class="text-red-600 font-medium">Expired</span>
                      <% else %>
                        <span class="text-gray-600 font-medium"><%= connection.status %></span>
                      <% end %>
                    </span>
                    
                    <% if connection.account_count > 0 %>
                      <span class="ml-3 text-sm text-gray-500">
                        <%= pluralize(connection.account_count, 'account') %>
                      </span>
                    <% end %>
                  </div>
                  
                  <% if connection.last_synced_at %>
                    <p class="text-xs text-gray-400 mt-1">
                      Last synced: <%= time_ago_in_words(connection.last_synced_at) %> ago
                    </p>
                  <% end %>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <%= link_to gocardless_connection_path(connection),
                    class: "text-blue-600 hover:text-blue-900 text-sm font-medium" do %>
                  View Details
                <% end %>
                
                <%= link_to gocardless_connection_path(connection), 
                    method: :delete,
                    data: { 
                      confirm: "Are you sure you want to remove this bank connection?" 
                    },
                    class: "text-red-600 hover:text-red-900 text-sm font-medium" do %>
                  Remove
                <% end %>
              </div>
            </div>
            
            <% if connection.last_error.present? %>
              <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Connection Error</h3>
                    <div class="mt-2 text-sm text-red-700">
                      <p><%= connection.last_error %></p>
                      <% if connection.last_error_at %>
                        <p class="text-xs mt-1">
                          Occurred: <%= time_ago_in_words(connection.last_error_at) %> ago
                        </p>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  <% else %>
    <div class="text-center py-12">
      <div class="text-gray-400 mb-4">
        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No bank connections</h3>
      <p class="text-gray-600 mb-6">Connect your bank account to automatically sync transactions and account data.</p>
      <%= link_to new_gocardless_connection_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Connect Your First Bank Account
      <% end %>
    </div>
  <% end %>
</div>
