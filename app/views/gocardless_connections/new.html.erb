<% content_for :title, "Connect Bank Account" %>

<div class="max-w-2xl mx-auto">
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-xl font-semibold text-gray-900">Connect Your Bank Account</h1>
      <p class="mt-1 text-sm text-gray-600">
        Connect your bank account securely using GoCardless Bank Account Data to automatically sync your transactions.
      </p>
    </div>

    <div class="p-6">
      <% if @institutions.present? %>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">
            Select your bank
          </label>

          <div class="space-y-3 max-h-96 overflow-y-auto">
            <% @institutions.each do |institution| %>
              <%= form_with url: gocardless_connections_path, method: :post, local: true, class: "block w-full bank-form", data: { turbo: false } do |form| %>
                <%= hidden_field_tag :institution_id, institution["id"] %>

                <button type="submit" class="bank-button w-full p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900 mb-1">
                        <%= institution["name"] %>
                      </h3>
                      <% if institution["bic"].present? %>
                        <p class="text-xs text-gray-500">BIC: <%= institution["bic"] %></p>
                      <% end %>
                    </div>

                    <div class="flex items-center space-x-3">
                      <% if institution["logo"].present? %>
                        <img src="<%= institution["logo"] %>"
                             alt="<%= institution["name"] %> logo"
                             class="w-8 h-8 object-contain">
                      <% end %>

                      <!-- Loading spinner (hidden by default) -->
                      <div class="loading-spinner hidden">
                        <svg class="animate-spin h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>

                      <!-- Arrow icon (shown by default) -->
                      <svg class="arrow-icon w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </button>
              <% end %>
            <% end %>
          </div>
        </div>

        <div class="flex items-center justify-between pt-4 border-t border-gray-200 mt-6">
          <%= link_to "Cancel", gocardless_connections_path,
              class: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <div class="text-gray-400 mb-4">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No banks available</h3>
          <p class="text-gray-600 mb-4">Unable to load the list of available banks. Please try again.</p>
          <%= link_to "Try Again", new_gocardless_connection_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" %>
        </div>
      <% end %>
    </div>
  </div>

  <% if Rails.env.development? %>
  <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-yellow-800">🧪 Development Mode - Sandbox Testing</h3>
        <div class="mt-2 text-sm text-yellow-700">
          <p><strong>Banks marked with 🧪 are recommended for testing.</strong> These will create sample accounts with mock transactions instead of requiring real bank authentication.</p>
          <p class="mt-1">For production testing with real banks, you'll need actual bank credentials.</p>
        </div>
      </div>
    </div>
  </div>
  <% end %>

  <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">Secure Connection</h3>
        <div class="mt-2 text-sm text-blue-700">
          <p>Your bank connection is secured by GoCardless and uses Open Banking standards. We never store your banking credentials.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle bank selection with loading states
  const bankForms = document.querySelectorAll('.bank-form');

  bankForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      const button = form.querySelector('.bank-button');
      const spinner = form.querySelector('.loading-spinner');
      const arrow = form.querySelector('.arrow-icon');
      const bankName = form.querySelector('h3').textContent.trim();

      // Disable all bank buttons to prevent multiple submissions
      document.querySelectorAll('.bank-button').forEach(btn => {
        if (btn !== button) {
          btn.disabled = true;
          btn.classList.add('opacity-30', 'cursor-not-allowed');
          btn.querySelector('h3').textContent = 'Please wait...';
        }
      });

      // Show loading state for clicked button
      if (spinner && arrow) {
        spinner.classList.remove('hidden');
        arrow.classList.add('hidden');
      }

      // Update button text to show progress
      const h3 = button.querySelector('h3');
      if (h3) {
        h3.textContent = `Connecting to ${bankName}...`;
      }

      // Show global loading message
      showLoadingMessage('Connecting to your bank...');
    });
  });

  function showLoadingMessage(message) {
    // Create or update loading overlay
    let overlay = document.getElementById('loading-overlay');
    if (!overlay) {
      overlay = document.createElement('div');
      overlay.id = 'loading-overlay';
      overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      overlay.innerHTML = `
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Connecting...</h3>
          <p class="text-sm text-gray-600 mb-4" id="loading-message">${message}</p>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p class="text-blue-800 text-xs">
              You'll be redirected to your bank to authorize the connection.
              This process is secure and encrypted.
            </p>
          </div>
          <button onclick="cancelConnection()" class="text-sm text-gray-500 hover:text-gray-700 underline">
            Cancel and go back
          </button>
        </div>
      `;
      document.body.appendChild(overlay);
    } else {
      document.getElementById('loading-message').textContent = message;
      overlay.classList.remove('hidden');
    }
  }

  function cancelConnection() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.remove();
    }

    // Re-enable all bank buttons
    document.querySelectorAll('.bank-button').forEach(btn => {
      btn.disabled = false;
      btn.classList.remove('opacity-30', 'cursor-not-allowed');
    });

    // Restore original bank names
    location.reload();
  }

  // Handle page visibility changes (user comes back from GoCardless)
  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      // User came back to the page, hide loading overlay
      const overlay = document.getElementById('loading-overlay');
      if (overlay) {
        overlay.classList.add('hidden');
      }
    }
  });
});
</script>


