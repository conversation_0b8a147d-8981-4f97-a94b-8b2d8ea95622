<% content_for :title, "Connect Bank Account" %>

<div class="max-w-2xl mx-auto">
  <div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-xl font-semibold text-gray-900">Connect Your Bank Account</h1>
      <p class="mt-1 text-sm text-gray-600">
        Connect your bank account securely using GoCardless Bank Account Data to automatically sync your transactions.
      </p>
    </div>

    <div class="p-6">
      <% if @institutions.present? %>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">
            Select your bank
          </label>

          <div class="space-y-2 max-h-96 overflow-y-auto">
            <% @institutions.each do |institution| %>
              <%= form_with url: gocardless_connections_path, method: :post, local: true, class: "w-full" do |form| %>
                <%= hidden_field_tag :institution_id, institution["id"] %>

                <button type="submit" class="relative flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer w-full text-left transition-colors duration-200">
                  <div class="flex items-center w-full">
                    <div class="w-4 h-4 border-2 border-gray-300 rounded-full bg-white">
                      <div class="w-2 h-2 bg-blue-600 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0"></div>
                    </div>

                    <div class="ml-3 flex-1">
                      <div class="flex items-center justify-between">
                        <div>
                          <h3 class="text-sm font-medium text-gray-900">
                            <%= institution["name"] %>
                          </h3>
                          <% if institution["bic"].present? %>
                            <p class="text-xs text-gray-500">BIC: <%= institution["bic"] %></p>
                          <% end %>
                        </div>

                        <% if institution["logo"].present? %>
                          <img src="<%= institution["logo"] %>"
                               alt="<%= institution["name"] %> logo"
                               class="w-8 h-8 object-contain">
                        <% end %>
                      </div>
                    </div>

                    <div class="ml-3">
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </button>
              <% end %>
            <% end %>
          </div>
        </div>

        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
          <%= link_to "Cancel", gocardless_connections_path,
              class: "px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <div class="text-gray-400 mb-4">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No banks available</h3>
          <p class="text-gray-600 mb-4">Unable to load the list of available banks. Please try again.</p>
          <%= link_to "Try Again", new_gocardless_connection_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" %>
        </div>
      <% end %>
    </div>
  </div>

  <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">Secure Connection</h3>
        <div class="mt-2 text-sm text-blue-700">
          <p>Your bank connection is secured by GoCardless and uses Open Banking standards. We never store your banking credentials.</p>
        </div>
      </div>
    </div>
  </div>
</div>


