<% content_for :title, @connection.institution_name %>

<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900"><%= @connection.institution_name %></h1>
      <p class="mt-1 text-sm text-gray-600">
        Bank connection details and account information
      </p>
    </div>
    
    <div class="flex items-center space-x-3">
      <%= link_to gocardless_connections_path,
          class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Connections
      <% end %>

      <% if @connection.linked? %>
        <%= link_to sync_gocardless_connection_path(@connection),
            method: :post,
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Sync Transactions
        <% end %>
      <% end %>

      <%= link_to gocardless_connection_path(@connection),
          method: :delete,
          data: {
            confirm: "Are you sure you want to remove this bank connection?"
          },
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Remove Connection
      <% end %>
    </div>
  </div>

  <!-- Connection Status -->
  <div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">Connection Status</h2>
    </div>
    <div class="p-6">
      <div class="flex items-center">
        <% case @connection.status %>
        <% when "LN" %>
          <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-green-800">Connected</h3>
            <p class="text-sm text-green-600">Your bank account is successfully connected and ready to sync data.</p>
          </div>
        <% when "CR", "GC", "UA", "SA", "GA" %>
          <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-yellow-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-yellow-800">In Progress</h3>
            <p class="text-sm text-yellow-600">
              <% case @connection.status %>
              <% when "CR" %>
                Connection created, waiting for user authentication.
              <% when "GC" %>
                User is giving consent for data access.
              <% when "UA" %>
                User is authenticating with their bank.
              <% when "SA" %>
                User is selecting which accounts to connect.
              <% when "GA" %>
                User is granting final access permissions.
              <% end %>
            </p>
            <div class="mt-2">
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p class="text-yellow-800 text-xs">
                  This page will automatically update when your connection is complete.
                  You can safely close this tab and return later.
                </p>
              </div>
            </div>
          </div>
        <% else %>
          <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-red-800">
              <% case @connection.status %>
              <% when "RJ" %>
                Rejected
              <% when "SU" %>
                Suspended
              <% when "EX" %>
                Expired
              <% else %>
                Error
              <% end %>
            </h3>
            <p class="text-sm text-red-600">
              <% case @connection.status %>
              <% when "RJ" %>
                The bank connection was rejected or cancelled by the user.
              <% when "SU" %>
                The bank connection has been suspended.
              <% when "EX" %>
                The bank connection has expired and needs to be renewed.
              <% else %>
                There was an issue with the bank connection.
              <% end %>
            </p>
          </div>
        <% end %>
      </div>
      
      <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <dt class="text-sm font-medium text-gray-500">Requisition ID</dt>
          <dd class="mt-1 text-sm text-gray-900 font-mono"><%= @connection.requisition_id %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Institution ID</dt>
          <dd class="mt-1 text-sm text-gray-900 font-mono"><%= @connection.institution_id %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500">Connected Accounts</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= pluralize(@connection.account_count, 'account') %></dd>
        </div>
      </div>
      
      <% if @connection.last_synced_at %>
        <div class="mt-4">
          <dt class="text-sm font-medium text-gray-500">Last Synced</dt>
          <dd class="mt-1 text-sm text-gray-900">
            <%= @connection.last_synced_at.strftime("%B %d, %Y at %I:%M %p") %>
            (<%= time_ago_in_words(@connection.last_synced_at) %> ago)
          </dd>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Account Data -->
  <% if @connection.linked? && @accounts_data.present? %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Connected Accounts</h2>
      </div>
      <div class="divide-y divide-gray-200">
        <% @accounts_data.each do |account| %>
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">
                  <%= account[:details]["account"]["name"] || "Account" %>
                </h3>
                
                <% if account[:details]["account"]["iban"] %>
                  <p class="text-sm text-gray-600 font-mono mt-1">
                    IBAN: <%= account[:details]["account"]["iban"] %>
                  </p>
                <% end %>
                
                <% if account[:details]["account"]["currency"] %>
                  <p class="text-sm text-gray-600 mt-1">
                    Currency: <%= account[:details]["account"]["currency"] %>
                  </p>
                <% end %>
              </div>
              
              <div class="ml-6 text-right">
                <% if account[:balances]["balances"].present? %>
                  <% balance = account[:balances]["balances"].first %>
                  <% if balance["balanceAmount"] %>
                    <p class="text-lg font-semibold text-gray-900">
                      <%= balance["balanceAmount"]["currency"] %> <%= balance["balanceAmount"]["amount"] %>
                    </p>
                    <p class="text-sm text-gray-500">
                      <%= balance["balanceType"]&.humanize || "Balance" %>
                    </p>
                  <% end %>
                <% end %>
              </div>
            </div>
            
            <% if account[:details]["account"]["ownerName"] %>
              <div class="mt-4">
                <dt class="text-sm font-medium text-gray-500">Account Holder</dt>
                <dd class="mt-1 text-sm text-gray-900"><%= account[:details]["account"]["ownerName"] %></dd>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  <% elsif @connection.linked? %>
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="p-6 text-center">
        <div class="text-gray-400 mb-4">
          <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Loading Account Data</h3>
        <p class="text-gray-600">Account information is being fetched from your bank. Please check back in a moment.</p>
      </div>
    </div>
  <% end %>

  <!-- Error Display -->
  <% if @connection.last_error.present? %>
    <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Connection Error</h3>
          <div class="mt-2 text-sm text-red-700">
            <p><%= @connection.last_error %></p>
            <% if @connection.last_error_at %>
              <p class="text-xs mt-1">
                Occurred: <%= @connection.last_error_at.strftime("%B %d, %Y at %I:%M %p") %>
              </p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<% if @connection.pending? %>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto-refresh for pending connections
  let refreshCount = 0;
  const maxRefreshes = 20; // Stop after 20 refreshes (10 minutes)

  function refreshStatus() {
    refreshCount++;

    if (refreshCount > maxRefreshes) {
      console.log('Max refresh attempts reached');
      return;
    }

    // Refresh the page to check for status updates
    setTimeout(() => {
      window.location.reload();
    }, 30000); // Refresh every 30 seconds
  }

  // Start the refresh cycle
  refreshStatus();

  // Show a subtle indicator that auto-refresh is active
  const statusSection = document.querySelector('.bg-yellow-100').closest('.flex');
  if (statusSection) {
    const indicator = document.createElement('div');
    indicator.className = 'mt-2 text-xs text-yellow-600';
    indicator.innerHTML = '🔄 Auto-refreshing every 30 seconds...';
    statusSection.appendChild(indicator);
  }
});
</script>
<% end %>
