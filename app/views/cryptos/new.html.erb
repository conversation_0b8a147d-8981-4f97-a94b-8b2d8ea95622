<% if params[:step] == "method_select" %>
  <%= render "accounts/new/method_selector",
             path: new_crypto_path(return_to: params[:return_to]),
             show_us_link: @show_us_link,
             show_eu_link: @show_eu_link,
             accountable_type: "Crypto" %>
<% else %>
  <%= render DialogComponent.new do |dialog| %>
    <% dialog.with_header(title: t(".title")) %>
    <% dialog.with_body do %>
      <%= render "form", account: @account, url: cryptos_path %>
    <% end %>
  <% end %>
<% end %>
