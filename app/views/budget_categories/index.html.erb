<%= content_for :header_nav do %>
  <%= render "budgets/budget_nav", budget: @budget %>
<% end %>

<%= content_for :previous_path, edit_budget_path(@budget) %>
<%= content_for :cancel_path, budget_path(@budget) %>

<div>
  <div class="space-y-6">
    <div class="text-center space-y-2">
      <h1 class="text-3xl text-primary font-medium">Edit your category budgets</h1>
      <p class="text-secondary text-sm max-w-md mx-auto">
        Adjust category budgets to set spending limits. Unallocated funds will be automatically assigned as uncategorized.
      </p>
    </div>

    <div class="mx-auto max-w-lg">
      <% if @budget.family.categories.empty? %>
        <div class="bg-container shadow-border-xs rounded-lg p-4">
          <%= render "budget_categories/no_categories" %>
        </div>
      <% else %>
        <div class="max-w-md mx-auto">
          <%= render "budget_categories/allocation_progress", budget: @budget %>

          <div class="space-y-4 mb-4">
            <% BudgetCategory::Group.for(@budget_categories).sort_by(&:name).each do |group| %>
              <div class="space-y-4">
                <%= render "budget_categories/budget_category_form", budget_category: group.budget_category %>

                <div class="space-y-4">
                  <% group.budget_subcategories.each do |budget_subcategory| %>
                    <div class="w-full flex items-center gap-4">
                      <div class="ml-4 flex items-center justify-center text-subdued">
                        <%= icon("corner-down-right") %>
                      </div>

                      <%= render "budget_categories/budget_category_form", budget_category: budget_subcategory %>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>

            <%= render "budget_categories/uncategorized_budget_category_form", budget: @budget %>
          </div>

          <%= render "budget_categories/confirm_button", budget: @budget %>
        </div>
      <% end %>
    </div>
  </div>
</div>
