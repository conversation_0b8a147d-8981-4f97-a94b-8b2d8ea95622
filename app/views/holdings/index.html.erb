<%= turbo_frame_tag dom_id(@account, "holdings") do %>
  <div class="bg-container space-y-4 p-5 rounded-xl shadow-border-xs">
    <div class="flex items-center justify-between">
      <%= tag.h2 t(".holdings"), class: "font-medium text-lg" %>
      <%= link_to new_trade_path(account_id: @account.id),
                  id: dom_id(@account, "new_trade"),
                  data: { turbo_frame: :modal },
                  class: "flex gap-1 font-medium items-center bg-gray-50 text-primary p-2 rounded-lg" do %>
        <span class="text-primary">
          <%= icon("plus", color: "current") %>
        </span>
        <%= tag.span t(".new_holding"), class: "text-sm" %>
      <% end %>
    </div>

    <div class="bg-container-inset rounded-xl p-1">
      <div class="grid grid-cols-12 items-center uppercase text-xs font-medium text-secondary px-4 py-2">
        <%= tag.p t(".name"), class: "col-span-4" %>
        <%= tag.p t(".weight"), class: "col-span-2 justify-self-end" %>
        <%= tag.p t(".average_cost"), class: "col-span-2 justify-self-end" %>
        <%= tag.p t(".holdings"), class: "col-span-2 justify-self-end" %>
        <%= tag.p t(".return"), class: "col-span-2 justify-self-end" %>
      </div>

      <div class="bg-container rounded-lg shadow-border-xs">
        <%= render "holdings/cash", account: @account %>
        <%= render "shared/ruler" %>

        <% if @account.current_holdings.any? %>
          <%= render partial: "holdings/holding", collection: @account.current_holdings, spacer_template: "shared/ruler" %>
        <% end %>
      </div>
    </div>
  </div>
<% end %>
