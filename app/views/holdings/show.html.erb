<%= render DialogComponent.new(variant: "drawer") do |dialog| %>
  <% dialog.with_header do %>
    <div class="flex items-center justify-between">
      <div>
        <%= tag.h3 @holding.name, class: "text-2xl font-medium text-primary" %>
        <%= tag.p @holding.ticker, class: "text-sm text-secondary" %>
      </div>

      <%= image_tag "https://logo.synthfinance.com/ticker/#{@holding.ticker}", loading: "lazy", class: "w-9 h-9 rounded-full" %>
    </div>
  <% end %>

  <% dialog.with_body do %>
    <% dialog.with_section(title: t(".overview"), open: true) do %>
      <div class="pb-4">
        <dl class="space-y-3 px-3 py-2">
          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary"><%= t(".ticker_label") %></dt>
            <dd class="text-primary"><%= @holding.ticker %></dd>
          </div>

          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary"><%= t(".current_market_price_label") %></dt>
            <dd class="text-primary"><%= @holding.security.current_price ? format_money(@holding.security.current_price) : t(".unknown") %></dd>
          </div>

          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary"><%= t(".portfolio_weight_label") %></dt>
            <dd class="text-primary"><%= @holding.weight ? number_to_percentage(@holding.weight, precision: 2) : t(".unknown") %></dd>
          </div>

          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary"><%= t(".avg_cost_label") %></dt>
            <dd class="text-primary"><%= @holding.avg_cost ? format_money(@holding.avg_cost) : t(".unknown") %></dd>
          </div>

          <div class="flex items-center justify-between text-sm">
            <dt class="text-secondary"><%= t(".total_return_label") %></dt>
            <dd style="color: <%= @holding.trend&.color %>;">
              <%= @holding.trend ? render("shared/trend_change", trend: @holding.trend) : t(".unknown") %>
            </dd>
          </div>
        </dl>
      </div>
    <% end %>

    <% dialog.with_section(title: t(".history"), open: true) do %>
      <div class="space-y-2">
        <div class="px-3 py-4">
          <% if @holding.trades.any? %>
            <ul class="space-y-2">
              <% @holding.trades.each_with_index do |trade_entry, index| %>
                <li class="flex gap-4 text-sm space-y-1">
                  <div class="flex flex-col items-center gap-1.5 pt-2">
                    <div class="rounded-full h-1.5 w-1.5 bg-gray-300"></div>
                    <% unless index == @holding.trades.length - 1 %>
                      <div class="h-12 w-px bg-alpha-black-200"></div>
                    <% end %>
                  </div>

                  <div>
                    <p class="text-secondary text-xs uppercase"><%= l(trade_entry.date, format: :long) %></p>

                    <p><%= t(
                      ".trade_history_entry",
                      qty: trade_entry.trade.qty,
                      security: trade_entry.trade.security.ticker,
                      price: trade_entry.trade.price_money.format
                    ) %></p>
                  </div>
                </li>
              <% end %>
            </ul>

          <% else %>
            <p class="text-secondary">No trade history available for this holding.</p>
          <% end %>
        </div>
      </div>
    <% end %>

    <% unless @holding.account.plaid_account_id.present? %>
      <% dialog.with_section(title: t(".settings"), open: true) do %>
        <div class="pb-4">
          <div class="flex items-center justify-between gap-2 p-3">
            <div class="text-sm space-y-1">
              <h4 class="text-primary"><%= t(".delete_title") %></h4>
              <p class="text-secondary"><%= t(".delete_subtitle") %></p>
            </div>

            <%= button_to t(".delete"),
                holding_path(@holding),
                method: :delete,
                class: "rounded-lg px-3 py-2 text-red-500 text-sm font-medium border border-secondary",
                data: { turbo_confirm: true } %>
          </div>
        </div>
      <% end %>
    <% end %>
  <% end %>
<% end %>
