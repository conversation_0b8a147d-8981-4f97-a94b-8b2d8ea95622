<%# locals: (date:, entries:, content:, totals: false) %>

<div id="entry-group-<%= date %>" class="bg-container-inset rounded-xl p-1 w-full" data-bulk-select-target="group">
  <div class="py-2 px-4 flex items-center justify-between font-medium text-xs text-secondary">
    <div class="flex pl-0.5 items-center gap-4">
        <%= check_box_tag "#{date}_entries_selection",
                          class: ["checkbox checkbox--light", "hidden": entries.size == 0],
                          id: "selection_entry_#{date}",
                          data: { action: "bulk-select#toggleGroupSelection" } %>

      <p class="uppercase space-x-1.5">
        <%= tag.span I18n.l(date, format: :long) %>
        <span>·</span>
        <%= tag.span entries.size %>
      </p>
    </div>

    <% if totals %>
      <div id="entry-group-<%= date %>-totals">
        <%= totals_by_currency(collection: entries, money_method: :amount_money, negate: true) %>
      </div>
    <% end %>
  </div>
  <div class="bg-container shadow-border-xs rounded-lg">
    <%= content %>
  </div>
</div>
