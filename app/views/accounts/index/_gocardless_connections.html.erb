<%# locals: (connections:) %>

<% connections.each do |connection| %>
  <div class="bg-container rounded-xl shadow-border-xs p-4">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center gap-3">
        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
          <%= icon("building-2", class: "w-5 h-5 text-purple-600") %>
        </div>
        <div>
          <h3 class="font-medium text-primary">
            <%= link_to connection.institution_name, gocardless_connection_path(connection), 
                class: "hover:underline" %>
          </h3>
          <p class="text-sm text-secondary">
            GoCardless Open Banking • 
            <% case connection.status %>
            <% when "LN" %>
              <span class="text-green-600">Connected</span>
            <% when "CR", "GC", "UA", "SA", "GA" %>
              <span class="text-yellow-600">In Progress</span>
            <% else %>
              <span class="text-red-600">
                <%= connection.status == "RJ" ? "Rejected" : 
                    connection.status == "EX" ? "Expired" : 
                    connection.status == "SU" ? "Suspended" : "Error" %>
              </span>
            <% end %>
          </p>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <% if connection.linked? %>
          <%= link_to sync_gocardless_connection_path(connection), 
              method: :post,
              class: "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100" do %>
            <%= icon("refresh-cw", class: "w-3 h-3 mr-1") %>
            Sync
          <% end %>
        <% end %>
        
        <%= render MenuComponent.new(variant: "button") do |menu| %>
          <% menu.with_button(icon: "more-horizontal", variant: "ghost", size: "sm") %>
          
          <% menu.with_item(
            variant: "link",
            text: "View details",
            icon: "eye",
            href: gocardless_connection_path(connection)
          ) %>
          
          <% if connection.linked? %>
            <% menu.with_item(
              variant: "link",
              text: "Sync now",
              icon: "refresh-cw",
              href: sync_gocardless_connection_path(connection),
              data: { method: :post }
            ) %>
          <% end %>
          
          <% menu.with_item(
            variant: "link",
            text: "Remove",
            icon: "trash-2",
            href: gocardless_connection_path(connection),
            data: { 
              method: :delete,
              confirm: "Are you sure you want to remove this bank connection?"
            }
          ) %>
        <% end %>
      </div>
    </div>
    
    <% if connection.linked? && connection.account_count > 0 %>
      <div class="text-sm text-secondary">
        <%= pluralize(connection.account_count, 'account') %> connected
        <% if connection.last_synced_at %>
          • Last synced <%= time_ago_in_words(connection.last_synced_at) %> ago
        <% end %>
      </div>
    <% elsif connection.pending? %>
      <div class="text-sm text-yellow-600">
        Connection in progress - please complete the setup in your bank's app
      </div>
    <% elsif connection.failed? %>
      <div class="text-sm text-red-600">
        Connection failed - please try connecting again
      </div>
    <% end %>
  </div>
<% end %>
