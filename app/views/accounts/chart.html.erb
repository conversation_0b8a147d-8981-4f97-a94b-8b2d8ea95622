<% series = @account.balance_series(period: @period, view: @chart_view) %>
<% trend = series.trend %>

<%= turbo_frame_tag dom_id(@account, :chart_details) do %>
  <div class="px-4">
    <%= render partial: "shared/trend_change", locals: { trend: trend, comparison_label: @period.comparison_label } %>
  </div>

  <div class="h-64 pb-4">
    <% if series.any? %>
      <div
        id="lineChart"
        class="w-full h-full"
        data-controller="time-series-chart"
        data-time-series-chart-data-value="<%= series.to_json %>"></div>
    <% else %>
      <div class="w-full h-full flex items-center justify-center">
        <p class="text-secondary text-sm"><%= t(".data_not_available") %></p>
      </div>
    <% end %>
  </div>
<% end %>
