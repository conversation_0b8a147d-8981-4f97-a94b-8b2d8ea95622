<%# locals: (account:, url:) %>

<%= styled_form_with model: account, url: url, scope: :account, data: { turbo: false }, class: "flex flex-col gap-4 justify-between grow text-primary" do |form| %>
  <div class="grow space-y-2">
    <%= form.hidden_field :accountable_type %>
    <%= form.hidden_field :return_to, value: params[:return_to] %>

    <%= form.text_field :name, placeholder: t(".name_placeholder"), required: "required", label: t(".name_label") %>

    <% unless account.linked? %>
      <%= form.money_field :balance, label: t(".balance"), required: true, default_currency: Current.family.currency %>
    <% end %>

    <%= yield form %>
  </div>

  <%= form.submit %>
<% end %>
