<%# locals: (account:, tooltip: nil, chart_view: nil, **args) %>

<% period =  @period || Period.last_30_days %>
<% default_value_title = account.asset? ? t(".balance") : t(".owed") %>

<div id="<%= dom_id(account, :chart) %>" class="bg-container shadow-border-xs rounded-xl space-y-2">
  <div class="flex justify-between flex-col-reverse lg:flex-row gap-2 px-4 pt-4 mb-2">
    <div class="space-y-2 w-full">
      <div class="flex items-center gap-1">
        <%= tag.p account.investment? ? "Total value" : default_value_title, class: "text-sm font-medium text-secondary" %>

        <% if account.investment? %>
          <%= render "investments/value_tooltip", balance: account.balance_money, holdings: account.balance_money - account.cash_balance_money, cash: account.cash_balance_money %>
        <% end %>
      </div>
      <div class="flex flex-row gap-2 items-baseline">
        <%= tag.p format_money(account.balance_money), class: "text-primary text-3xl font-medium truncate" %>
        <% if account.currency != Current.family.currency %>
          <%= tag.p format_money(account.balance_money.exchange_to(Current.family.currency, fallback_rate: 1)), class: "text-sm font-medium text-secondary" %>
        <% end %>
      </div>
    </div>

    <%= form_with url: request.path, method: :get, data: { controller: "auto-submit-form" } do |form| %>
      <div class="flex items-center gap-2">
        <% if chart_view.present? %>
          <%= form.select :chart_view,
            [["Total value", "balance"], ["Holdings", "holdings_balance"], ["Cash", "cash_balance"]],
            { selected: chart_view },
            class: "bg-container border border-secondary rounded-lg text-sm pr-7 cursor-pointer text-primary focus:outline-hidden focus:ring-0",
            data: { "auto-submit-form-target": "auto" } %>
        <% end %>

        <%= form.select :period,
                    Period.as_options,
                    { selected: period.key },
                    data: { "auto-submit-form-target": "auto" },
                    class: "bg-container border border-secondary rounded-lg px-3 py-2 text-sm pr-7 cursor-pointer text-primary focus:outline-hidden focus:ring-0" %>
      </div>
    <% end %>
  </div>

  <%= turbo_frame_tag dom_id(account, :chart_details), src: chart_account_path(account, period: period.key, chart_view: chart_view) do %>
    <%= render "accounts/chart_loader" %>
  <% end %>
</div>
