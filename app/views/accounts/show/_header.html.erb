<%# locals: (account:, title: nil, subtitle: nil) %>

<header class="space-y-4">
  <div class="flex items-center gap-4">
    <% content = yield %>

    <% if content.present? %>
      <%= content %>
    <% else %>
      <div class="flex items-center gap-3 overflow-hidden">
        <%= render "accounts/logo", account: account %>

        <div class="flex items-center gap-2">
          <div class="truncate">
            <h2 class="font-medium text-xl truncate <%= "animate-pulse" if account.syncing? %>"><%= title || account.name %></h2>
            <% if subtitle.present? %>
              <p class="text-sm text-secondary"><%= subtitle %></p>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <div class="flex items-center gap-1 ml-auto">
      <% if Rails.env.development? || self_hosted? %>
        <%= icon(
            "refresh-cw",
            as_button: true,
            size: "sm",
            href: account.linked? ? sync_plaid_item_path(account.plaid_account.plaid_item) : sync_account_path(account),
            disabled: account.syncing?,
            frame: :_top
          ) %>
      <% end %>

      <%= render "accounts/show/menu", account: account %>
    </div>
  </div>
</header>
