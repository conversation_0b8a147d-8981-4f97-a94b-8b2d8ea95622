<%# locals: (account:) %>

<%= render MenuComponent.new(testid: "account-menu") do |menu| %>
  <% menu.with_item(variant: "link", text: "Edit", href: edit_account_path(account), icon: "pencil-line", data: { turbo_frame: :modal }) %>

  <% unless account.crypto? %>
    <% menu.with_item(
      variant: "link",
      text: "Import transactions",
      href: imports_path({ import: { type: account.investment? ? "TradeImport" : "TransactionImport", account_id: account.id } }),
      icon: "download",
      data: { turbo_frame: :_top }
    ) %>
  <% end %>

  <% unless account.linked? %>
    <% menu.with_item(
      variant: "button",
      text: "Delete account",
      href: account_path(account),
      method: :delete,
      icon: "trash-2",
      confirm: CustomConfirm.for_resource_deletion("account", high_severity: true),
      data: { turbo_frame: :_top }
    ) %>
  <% end %>
<% end %>
