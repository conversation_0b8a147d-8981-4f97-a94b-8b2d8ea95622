<%# locals: (account:, tabs:) %>

<% active_tab = tabs.find { |tab| tab[:key] == params[:tab] } || tabs.first %>

<%= render TabsComponent.new(active_tab: active_tab[:key], url_param_key: "tab") do |tabs_container| %>
  <% tabs_container.with_nav(classes: "max-w-fit") do |nav| %>
    <% tabs.each do |tab| %>
      <% nav.with_btn(id: tab[:key], label: tab[:key].humanize, classes: "px-6") %>
    <% end %>
  <% end %>

  <% tabs.each do |tab| %>
    <% tabs_container.with_panel(tab_id: tab[:key]) do %>
      <%= tab[:contents] %>
    <% end %>
  <% end %>
<% end %>
