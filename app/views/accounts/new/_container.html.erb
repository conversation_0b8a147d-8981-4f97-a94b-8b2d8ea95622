<%# locals: (title:, back_path: nil) %>

<%= render DialogComponent.new do |dialog| %>
  <div class="flex flex-col relative" data-controller="list-keyboard-navigation">
    <div class="border-b border-tertiary md:border-alpha-black-25 px-4 pb-4 text-gray-800 flex items-center justify-between gap-2">
      <div class="flex items-center gap-2">
        <% if back_path %>
          <%= render LinkComponent.new(
          variant: "icon",
          icon: "arrow-left",
          href: back_path,
          size: "lg"
        ) %>
        <% end %>

        <span class="text-primary"><%= title %></span>
      </div>

      <%= icon("x", as_button: true, size: "lg", data: { action: "dialog#close" }) %>
    </div>

    <div class="p-2 text-subdued">
      <button hidden data-controller="hotkey" data-hotkey="k,K,ArrowUp,ArrowLeft" data-action="list-keyboard-navigation#focusPrevious">Previous</button>
      <button hidden data-controller="hotkey" data-hotkey="j,J,<PERSON>D<PERSON>,<PERSON><PERSON><PERSON>" data-action="list-keyboard-navigation#focusNext">Next</button>

      <%= yield %>
    </div>

    <div class="border-t border-alpha-black-25 px-4 pt-4 text-secondary text-sm justify-between hidden md:flex">
      <div class="flex space-x-5">
        <div class="flex items-center space-x-2">
          <span>Select</span>
          <kbd class="bg-alpha-black-50 shadow-[inset_0_-1px_0_0_rgba(0,0,0,0.1)] p-1 rounded-md flex w-5 h-5 shrink-0 grow-0 items-center justify-center">
            <%= icon("corner-down-left", size: "xs") %>
          </kbd>
        </div>
        <div class="flex items-center space-x-2">
          <span>Navigate</span>
          <kbd class="bg-alpha-black-50 shadow-[inset_0_-1px_0_0_rgba(0,0,0,0.1)] p-1 rounded-md flex w-5 h-5 shrink-0 grow-0 items-center justify-center">
            <%= icon("arrow-up", size: "xs") %>
          </kbd>
          <kbd class="bg-alpha-black-50 shadow-[inset_0_-1px_0_0_rgba(0,0,0,0.1)] p-1 rounded-md flex w-5 h-5 shrink-0 grow-0 items-center justify-center">
            <%= icon("arrow-down", size: "xs") %>
          </kbd>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <button data-action="dialog#close">Close</button>
        <kbd class="bg-alpha-black-50 shadow-[inset_0_-1px_0_0_rgba(0,0,0,0.1)] p-1 rounded-md flex w-8 h-5 shrink-0 grow-0 items-center justify-center text-xs">ESC</kbd>
      </div>
    </div>
  </div>
<% end %>
