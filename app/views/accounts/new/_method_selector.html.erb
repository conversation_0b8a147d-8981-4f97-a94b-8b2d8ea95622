<%# locals: (path:, accountable_type:, show_us_link: true, show_eu_link: true, show_gocardless_link: true) %>

<%= render layout: "accounts/new/container", locals: { title: t(".title"), back_path: new_account_path } do %>
  <div class="text-sm space-y-3">
    <!-- Manual Entry -->
    <%= link_to path, class: "flex items-center gap-4 w-full text-center text-primary focus:outline-hidden focus:bg-surface border border-transparent focus:border focus:border-gray-200 px-2 hover:bg-surface rounded-lg p-3" do %>
      <span class="flex w-8 h-8 shrink-0 grow-0 items-center justify-center rounded-lg bg-alpha-black-50 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.02)]">
        <%= icon("keyboard") %>
      </span>
      <div class="flex-1 text-left">
        <div class="font-medium"><%= t("accounts.new.method_selector.manual_entry") %></div>
        <div class="text-xs text-gray-500">Enter account details manually</div>
      </div>
    <% end %>

    <!-- Bank Connections Section -->
    <div class="border-t pt-3">
      <h3 class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">Bank Connections</h3>

      <% if show_us_link %>
        <!-- US Banks (Plaid) -->
        <%= link_to new_plaid_item_path(region: "us", accountable_type: accountable_type),
                    class: "text-primary flex items-center gap-4 w-full text-center focus:outline-hidden focus:bg-gray-50 border border-transparent focus:border focus:border-gray-200 px-2 hover:bg-gray-50 rounded-lg p-3",
                    data: { turbo_frame: "modal" } do %>
          <span class="flex w-8 h-8 shrink-0 grow-0 items-center justify-center rounded-lg bg-blue-50 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.02)]">
            <%= icon("link-2") %>
          </span>
          <div class="flex-1 text-left">
            <div class="font-medium"><%= t("accounts.new.method_selector.connected_entry") %></div>
            <div class="text-xs text-gray-500">Connect US banks via Plaid</div>
          </div>
          <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">US</span>
        <% end %>
      <% end %>

      <% if show_eu_link %>
        <!-- EU Banks (Plaid EU) -->
        <%= link_to new_plaid_item_path(region: "eu", accountable_type: accountable_type),
                    class: "text-primary flex items-center gap-4 w-full text-center focus:outline-hidden focus:bg-gray-50 border border-transparent focus:border focus:border-gray-200 px-2 hover:bg-gray-50 rounded-lg p-3",
                    data: { turbo_frame: "modal" } do %>
          <span class="flex w-8 h-8 shrink-0 grow-0 items-center justify-center rounded-lg bg-green-50 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.02)]">
            <%= icon("link-2") %>
          </span>
          <div class="flex-1 text-left">
            <div class="font-medium"><%= t("accounts.new.method_selector.connected_entry_eu") %></div>
            <div class="text-xs text-gray-500">Connect EU banks via Plaid</div>
          </div>
          <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">EU</span>
        <% end %>
      <% end %>

      <% if show_gocardless_link %>
        <!-- UK/EU Open Banking (GoCardless) -->
        <%= link_to new_gocardless_connection_path(accountable_type: accountable_type),
                    class: "text-primary flex items-center gap-4 w-full text-center focus:outline-hidden focus:bg-gray-50 border border-transparent focus:border focus:border-gray-200 px-2 hover:bg-gray-50 rounded-lg p-3",
                    data: { turbo_frame: "modal" } do %>
          <span class="flex w-8 h-8 shrink-0 grow-0 items-center justify-center rounded-lg bg-purple-50 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.02)]">
            <%= icon("building-2") %>
          </span>
          <div class="flex-1 text-left">
            <div class="font-medium">Open Banking Connection</div>
            <div class="text-xs text-gray-500">Connect UK/EU banks via GoCardless</div>
          </div>
          <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">UK/EU</span>
        <% end %>
      <% end %>
    </div>

    <!-- Import Section -->
    <div class="border-t pt-3">
      <h3 class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">Import Data</h3>

      <%= button_to imports_path(import: { type: "AccountImport" }),
              data: { turbo_frame: :_top },
              class: "flex items-center gap-4 w-full text-center focus:outline-hidden hover:bg-surface-hover focus:bg-surface-hover fg-primary border border-transparent px-2 rounded-lg p-3" do %>
        <span class="flex w-8 h-8 shrink-0 grow-0 items-center justify-center rounded-lg bg-orange-50 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.02)]">
          <%= icon("download") %>
        </span>
        <div class="flex-1 text-left">
          <div class="font-medium">Import from CSV</div>
          <div class="text-xs text-gray-500">Upload account data from spreadsheet</div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
