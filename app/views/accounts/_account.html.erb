<%# locals: (account:, return_to: nil) %>

<%= turbo_frame_tag dom_id(account) do %>
  <div class="p-4 flex items-center justify-between gap-3 group/account">
    <div class="flex items-center gap-3">
      <%= render "accounts/logo", account: account, size: "md" %>

      <div>
        <% if account.scheduled_for_deletion? %>
          <p class="text-sm font-medium text-primary">
            <span>
              <%= account.name %>
            </span>
            <span class="text-red-500 animate-pulse">
              (deletion in progress...)
            </span>
          </p>
        <% else %>
          <%= link_to account.name, account, class: [(account.is_active ? "text-primary" : "text-subdued"), "text-sm font-medium hover:underline"], data: { turbo_frame: "_top" } %>
          <% if account.long_subtype_label %>
            <p class="text-sm text-secondary truncate"><%= account.long_subtype_label %></p>
          <% end %>
        <% end %>
      </div>

      <% unless account.scheduled_for_deletion? %>
        <%= link_to edit_account_path(account, return_to: return_to), data: { turbo_frame: :modal }, class: "group-hover/account:flex hidden hover:opacity-80 items-center justify-center" do %>
          <%= icon("pencil-line", size: "sm") %>
        <% end %>
      <% end %>
    </div>
    <div class="flex items-center gap-8">
      <% if account.syncing? %>
        <div class="w-16 h-6 bg-loader rounded-full animate-pulse"></div>
      <% else %>
        <p class="text-sm font-medium <%= account.is_active ? "text-primary" : "text-subdued" %>">
          <%= format_money account.balance_money %>
        </p>
      <% end %>

      <% unless account.scheduled_for_deletion? %>
        <%= styled_form_with model: account, data: { turbo_frame: "_top", controller: "auto-submit-form" } do |f| %>
          <%= f.toggle :is_active, { data: { auto_submit_form_target: "auto" } } %>
        <% end %>
      <% end %>
    </div>
  </div>
<% end %>
