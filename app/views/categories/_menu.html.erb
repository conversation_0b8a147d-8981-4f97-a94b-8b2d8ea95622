<%# locals: (transaction:) %>

<%= render MenuComponent.new(variant: "button") do |menu| %>
  <% menu.with_button do %>
    <% render partial: "categories/badge", locals: { category: transaction.category } %>
  <% end %>

  <% menu.with_custom_content do %>
    <%= turbo_frame_tag "category_dropdown", src: category_dropdown_path(category_id: transaction.category_id, transaction_id: transaction.id), loading: :lazy do %>
      <div class="p-6 flex items-center justify-center">
        <p class="text-sm text-secondary animate-pulse"><%= t(".loading") %></p>
      </div>
    <% end %>
  <% end %>
<% end %>
