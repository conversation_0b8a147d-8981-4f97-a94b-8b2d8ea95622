<%= render DialogComponent.new do |dialog| %>
  <% dialog.with_header(title: t(".delete_category"), subtitle: t(".explanation", category_name: @category.name)) %>

  <% dialog.with_body do %>
    <%= styled_form_with url: category_deletions_path(@category),
                       data: {
                         turbo: false,
                         controller: "deletion",
                         deletion_submit_text_when_not_replacing_value: t(".delete_and_leave_uncategorized", category_name: @category.name),
                         deletion_submit_text_when_replacing_value: t(".delete_and_recategorize", category_name: @category.name) } do |f| %>
      <%= f.collection_select :replacement_category_id,
                            Current.family.categories.alphabetically.without(@category),
                            :id, :name,
                            { prompt: t(".replacement_category_prompt"), label: t(".category"), container_class: "mb-4" },
                            data: { deletion_target: "replacementField", action: "deletion#chooseSubmitButton" } %>

      <%= render ButtonComponent.new(
        variant: "destructive",
        text: t(".delete_and_leave_uncategorized", category_name: @category.name),
        full_width: true,
        data: { deletion_target: "destructiveSubmitButton" }
      ) %>

      <%= render ButtonComponent.new(
        text: "Delete and reassign",
        data: { deletion_target: "safeSubmitButton" },
        hidden: true,
        full_width: true
      ) %>
    <% end %>
  <% end %>
<% end %>
