<% if params[:step] == "method_select" %>
  <%= render "accounts/new/method_selector",
             path: new_depository_path(return_to: params[:return_to]),
             show_us_link: @show_us_link,
             show_eu_link: @show_eu_link,
             show_gocardless_link: true,
             accountable_type: "Depository" %>
<% else %>
  <%= render DialogComponent.new do |dialog| %>
    <% dialog.with_header(title: t(".title")) %>
    <% dialog.with_body do %>
      <%= render "depositories/form", account: @account, url: depositories_path %>
    <% end %>
  <% end %>
<% end %>
