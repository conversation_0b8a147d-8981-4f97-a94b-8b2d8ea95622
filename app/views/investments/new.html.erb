<% if params[:step] == "method_select" %>
  <%= render "accounts/new/method_selector",
             path: new_investment_path(return_to: params[:return_to]),
             show_us_link: @show_us_link,
             show_eu_link: @show_eu_link,
             show_gocardless_link: true,
             accountable_type: "Investment" %>
<% else %>
  <%= render DialogComponent.new do |dialog| %>
    <% dialog.with_header(title: t(".title")) %>
    <% dialog.with_body do %>
      <%= render "investments/form", account: @account, url: investments_path %>
    <% end %>
  <% end %>
<% end %>
