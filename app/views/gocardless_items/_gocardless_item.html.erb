<%# locals: (gocardless_item:) %>

<%= tag.div id: dom_id(gocardless_item) do %>
  <details open class="group bg-container p-4 shadow-border-xs rounded-xl">
    <summary class="flex items-center justify-between gap-2 focus-visible:outline-hidden">
      <div class="flex items-center gap-2">
        <%= icon "chevron-right", class: "group-open:transform group-open:rotate-90" %>

        <div class="flex items-center justify-center h-8 w-8 bg-green-600/10 rounded-full">
          <div class="flex items-center justify-center">
            <%= tag.p gocardless_item.name.first.upcase, class: "text-green-600 text-xs font-medium" %>
          </div>
        </div>

        <div class="pl-1 text-sm">
          <div class="flex items-center gap-2">
            <%= tag.p gocardless_item.name, class: "font-medium text-primary" %>
            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">GoCardless</span>
            <% if gocardless_item.scheduled_for_deletion? %>
              <p class="text-destructive text-sm animate-pulse">(deletion in progress...)</p>
            <% end %>
          </div>
          <% if gocardless_item.syncing? %>
            <div class="text-secondary flex items-center gap-1">
              <%= icon "loader", size: "sm", class: "animate-pulse" %>
              <span>Syncing...</span>
            </div>
          <% elsif gocardless_item.sync_error.present? %>
            <div class="text-secondary flex items-center gap-1">
              <%= icon "alert-circle", size: "sm", color: "destructive" %>
              <span class="text-destructive">Error</span>
            </div>
          <% else %>
            <p class="text-secondary">
              <%= gocardless_item.last_synced_at ? "Last synced #{time_ago_in_words(gocardless_item.last_synced_at)} ago" : "Never synced" %>
            </p>
          <% end %>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <%= icon(
          "refresh-cw",
          as_button: true,
          href: sync_gocardless_item_path(gocardless_item),
          method: :post
        ) %>

        <%= render MenuComponent.new do |menu| %>
          <% menu.with_item(
            variant: "button",
            text: "View Details",
            icon: "eye",
            href: gocardless_item_path(gocardless_item)
          ) %>
          <% menu.with_item(
            variant: "button",
            text: "Delete",
            icon: "trash-2",
            href: gocardless_item_path(gocardless_item),
            method: :delete,
            confirm: CustomConfirm.new(
              title: "Delete bank connection?",
              body: "Are you sure you want to delete this bank connection? This will remove all associated accounts and transaction data. This action cannot be undone.",
              btn_text: "Delete Connection",
              destructive: true,
              high_severity: true
            )
          ) %>
        <% end %>
      </div>
    </summary>

    <% unless gocardless_item.scheduled_for_deletion? %>
      <div class="space-y-4 mt-4">
        <% if gocardless_item.accounts.any? %>
          <%= render "accounts/index/account_groups", accounts: gocardless_item.accounts %>
        <% else %>
          <div class="p-4 flex flex-col gap-3 items-center justify-center">
            <p class="text-primary font-medium text-sm">No accounts synced yet</p>
            <p class="text-secondary text-sm">Try syncing this connection to import your bank accounts.</p>
          </div>
        <% end %>
      </div>
    <% end %>
  </details>
<% end %> 