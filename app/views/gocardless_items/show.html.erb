<div class="max-w-4xl mx-auto p-6">
  <div class="flex justify-between items-start mb-6">
    <div>
      <h1 class="text-2xl font-bold text-primary mb-2"><%= @gocardless_item.name %></h1>
      <p class="text-secondary">GoCardless bank connection details and accounts</p>
    </div>
    <div class="flex space-x-2">
      <%= button_to "Sync Now", sync_gocardless_item_path(@gocardless_item), method: :patch, 
                    class: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700" %>
      <%= link_to "All Banks", gocardless_items_path, 
                  class: "px-4 py-2 border border-primary rounded-lg text-primary hover:bg-surface-hover" %>
    </div>
  </div>

  <!-- Connection Status -->
  <div class="bg-white border border-primary rounded-lg p-4 mb-6">
    <h2 class="font-medium text-primary mb-3">Connection Status</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <div class="text-sm text-secondary">Status</div>
        <div class="<%= @gocardless_item.active? ? 'text-green-600' : 'text-red-600' %> font-medium">
          <%= @gocardless_item.status.humanize %>
        </div>
      </div>
      <div>
        <div class="text-sm text-secondary">Institution ID</div>
        <div class="text-primary"><%= @gocardless_item.institution_id %></div>
      </div>
      <div>
        <div class="text-sm text-secondary">Requisition ID</div>
        <div class="text-primary font-mono text-sm"><%= @gocardless_item.requisition_id %></div>
      </div>
    </div>
    
    <% if @gocardless_item.last_synced_at %>
      <div class="mt-4 pt-4 border-t border-gray-200">
        <div class="text-sm text-secondary">Last Synced</div>
        <div class="text-primary">
          <%= @gocardless_item.last_synced_at.strftime("%B %d, %Y at %I:%M %p") %>
          (<%= time_ago_in_words(@gocardless_item.last_synced_at) %> ago)
        </div>
      </div>
    <% end %>
  </div>

  <!-- Connected Accounts -->
  <div class="bg-white border border-primary rounded-lg p-4 mb-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="font-medium text-primary">Connected Accounts</h2>
      <span class="text-sm text-secondary">
        <%= pluralize(@accounts.count, 'account') %>
      </span>
    </div>

    <% if @accounts.any? %>
      <div class="space-y-3">
        <% @accounts.each do |account| %>
          <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
            <div class="flex-1">
              <div class="font-medium text-primary">
                <%= link_to account.name, account_path(account), class: "hover:underline" %>
              </div>
              <div class="text-sm text-secondary">
                <%= account.accountable_type.humanize %> • <%= account.currency %>
              </div>
              <% if account.gocardless_account&.gocardless_account_id %>
                <div class="text-xs text-secondary font-mono">
                  GoCardless ID: <%= account.gocardless_account.gocardless_account_id %>
                </div>
              <% end %>
            </div>
            <div class="text-right">
              <div class="font-medium text-primary">
                <%= account.currency %> <%= number_with_delimiter(account.balance, precision: 2) %>
              </div>
              <div class="text-sm text-secondary">
                Current Balance
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center p-8 text-secondary">
        <p>No accounts have been synced yet.</p>
        <p class="text-sm mt-1">Try syncing this connection to import accounts.</p>
      </div>
    <% end %>
  </div>

  <!-- Sync History -->
  <% if @gocardless_item.syncs.any? %>
    <div class="bg-white border border-primary rounded-lg p-4">
      <h2 class="font-medium text-primary mb-4">Recent Sync History</h2>
      <div class="space-y-2">
        <% @gocardless_item.syncs.recent.limit(5).each do |sync| %>
          <div class="flex justify-between items-center p-2 text-sm">
            <div>
              <span class="<%= sync.completed? ? 'text-green-600' : sync.failed? ? 'text-red-600' : 'text-yellow-600' %>">
                <%= sync.status.humanize %>
              </span>
              <% if sync.error.present? %>
                <span class="text-red-600 ml-2">- <%= sync.error %></span>
              <% end %>
            </div>
            <div class="text-secondary">
              <%= sync.created_at.strftime("%m/%d/%Y %I:%M %p") %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Danger Zone -->
  <div class="mt-8 p-4 border border-red-200 rounded-lg bg-red-50">
    <h3 class="font-medium text-red-800 mb-2">Danger Zone</h3>
    <p class="text-sm text-red-600 mb-3">
      Removing this bank connection will permanently delete all associated accounts and transaction data.
      This action cannot be undone.
    </p>
    <%= button_to "Remove Bank Connection", gocardless_item_path(@gocardless_item), 
                  method: :delete,
                  confirm: "Are you sure you want to remove this bank connection? This will permanently delete all associated accounts and transaction data. This action cannot be undone.",
                  class: "bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700" %>
  </div>
</div> 