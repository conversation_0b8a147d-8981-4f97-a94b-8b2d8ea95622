<div class="max-w-4xl mx-auto p-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-primary mb-2">Connected Banks (GoCardless)</h1>
      <p class="text-secondary">Manage your UK/EU bank connections via GoCardless Open Banking.</p>
    </div>
    <%= link_to new_gocardless_item_path, class: "bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-hover" do %>
      Connect New Bank
    <% end %>
  </div>

  <% if @gocardless_items.any? %>
    <div class="space-y-4">
      <% @gocardless_items.each do |item| %>
        <div class="border border-primary rounded-lg p-4">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <h3 class="font-medium text-primary"><%= item.name %></h3>
              <p class="text-sm text-secondary mt-1">
                Status: 
                <span class="<%= item.active? ? 'text-green-600' : 'text-red-600' %>">
                  <%= item.status.humanize %>
                </span>
              </p>
              <p class="text-sm text-secondary">
                <%= pluralize(item.accounts.count, 'account') %> connected
              </p>
              <% if item.last_synced_at %>
                <p class="text-sm text-secondary">
                  Last synced: <%= time_ago_in_words(item.last_synced_at) %> ago
                </p>
              <% end %>
            </div>
            
            <div class="flex space-x-2">
              <%= link_to "View", gocardless_item_path(item), class: "px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200" %>
              <%= button_to "Sync", sync_gocardless_item_path(item), method: :patch, class: "px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200" %>
              <%= button_to "Remove", gocardless_item_path(item), method: :delete, 
                            confirm: "Are you sure? This will remove the bank connection and all associated accounts.",
                            class: "px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200" %>
            </div>
          </div>
          
          <% if item.accounts.any? %>
            <div class="mt-3 pt-3 border-t border-gray-200">
              <h4 class="text-sm font-medium text-secondary mb-2">Connected Accounts:</h4>
              <div class="space-y-1">
                <% item.accounts.each do |account| %>
                  <div class="flex justify-between items-center text-sm">
                    <span class="text-primary"><%= account.name %></span>
                    <span class="text-secondary">
                      <%= account.currency %> <%= number_with_delimiter(account.balance) %>
                    </span>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center p-12 border border-primary rounded-lg">
      <div class="text-secondary mb-4">
        <p class="text-lg">No banks connected yet</p>
        <p>Connect your UK or EU bank account to automatically import transactions and balances.</p>
      </div>
      <%= link_to "Connect Your First Bank", new_gocardless_item_path, class: "bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-hover" %>
    </div>
  <% end %>

  <div class="mt-8 p-4 bg-gray-50 rounded-lg">
    <h3 class="font-medium text-primary mb-2">About GoCardless Open Banking</h3>
    <ul class="text-sm text-secondary space-y-1">
      <li>• Secure, read-only access to your bank account data</li>
      <li>• Regulated by the Financial Conduct Authority (FCA)</li>
      <li>• No passwords stored - authentication happens directly with your bank</li>
      <li>• You can revoke access at any time</li>
    </ul>
  </div>
</div> 