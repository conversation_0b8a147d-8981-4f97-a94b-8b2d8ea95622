<div class="max-w-2xl mx-auto p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-primary mb-2">Connect Your UK/EU Bank Account</h1>
    <p class="text-secondary">Select your bank to securely connect your account via GoCardless Open Banking.</p>
  </div>

  <% if @provider.nil? %>
    <div class="text-center p-8 border border-red-200 rounded-lg bg-red-50">
      <div class="text-red-600 mb-4">
        <h3 class="font-medium mb-2">GoCardless Not Configured</h3>
        <p>GoCardless integration requires API keys to be configured.</p>
        <p class="text-sm mt-2">Please add the following environment variables to your .env.local file:</p>
      </div>
      <div class="bg-gray-100 p-4 rounded text-left text-sm font-mono mt-4">
        GOCARDLESS_SECRET_ID=your_secret_id<br>
        GOCARDLESS_SECRET_KEY=your_secret_key<br>
        GOCARDLESS_ENV=sandbox
      </div>
    </div>
  <% elsif @institutions.empty? %>
    <div class="text-center p-8 border border-red-200 rounded-lg bg-red-50">
      <h3 class="font-medium text-red-600 mb-2">Failed to fetch institutions</h3>
      <p class="text-red-600">Unable to load bank list. Please check your GoCardless configuration and try again.</p>
    </div>
  <% else %>
      <form action="/gocardless_items" method="post" class="space-y-6" data-turbo="false" data-controller="bank-selector">
        <%= hidden_field_tag :authenticity_token, form_authenticity_token %>

        <!-- Step 1: Bank Selection -->
        <div>
          <h2 class="text-lg font-medium text-primary mb-4">
            <strong>Step 1:</strong> Choose your bank
          </h2>

          <div class="grid gap-3 max-h-96 overflow-y-auto">
            <% @institutions.each do |institution| %>
              <label class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-surface-hover cursor-pointer transition-colors duration-200">

                <input type="radio"
                       name="institution_id"
                       value="<%= institution[:id] %>"
                       class="mr-4"
                       data-action="change->bank-selector#bankSelected" />

                <!-- Bank Logo -->
                <div class="flex-shrink-0 w-12 h-12 mr-4">
                  <img src="<%= institution[:logo] %>"
                       alt="<%= institution[:name] %> logo"
                       class="w-full h-full object-contain rounded" />
                </div>

                <!-- Bank Info -->
                <div class="flex-grow">
                  <h3 class="font-medium text-gray-900"><%= institution[:name] %></h3>
                  <p class="text-sm text-gray-500">BIC: <%= institution[:bic] %></p>
                </div>
              </label>
            <% end %>
          </div>
        </div>

        <!-- Step 2: Submit Button -->
        <div class="border-t pt-6">
          <div class="text-center">
            <p id="selection_hint" class="text-sm text-secondary mb-4">
              <strong>Please select a bank above to continue</strong>
            </p>

            <input type="submit" value="Connect Bank Account"
                   data-bank-selector-target="submit"
                   class="button-bg-primary hover:button-bg-primary-hover text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 cursor-pointer opacity-50 cursor-not-allowed"
                   disabled />
          </div>
        </div>
      </form>
    </div>

    <!-- About Section -->
    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
      <h3 class="font-medium text-gray-900 mb-2">About GoCardless Open Banking</h3>
      <ul class="text-sm text-gray-600 space-y-1">
        <li>• Secure connection to your bank account</li>
        <li>• Read-only access to transactions and balances</li>
        <li>• No ability to move money or make payments</li>
        <li>• You can revoke access at any time</li>
      </ul>
    </div>
  <% end %>