<div class="max-w-2xl mx-auto p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-primary mb-2">Connect Your UK/EU Bank Account</h1>
    <p class="text-secondary">Select your bank to securely connect your account via GoCardless Open Banking.</p>
  </div>

  <% if @provider.nil? %>
    <div class="text-center p-8 border border-red-200 rounded-lg bg-red-50">
      <div class="text-red-600 mb-4">
        <h3 class="font-medium mb-2">GoCardless Not Configured</h3>
        <p>GoCardless integration requires API keys to be configured.</p>
        <p class="text-sm mt-2">Please add the following environment variables to your .env.local file:</p>
      </div>
      <div class="bg-gray-100 p-4 rounded text-left text-sm font-mono mt-4">
        GOCARDLESS_SECRET_ID=your_secret_id<br>
        GOCARDLESS_SECRET_KEY=your_secret_key<br>
        GOCARDLESS_ENV=sandbox
      </div>
    </div>
  <% elsif @institutions.empty? %>
    <div class="text-center p-8 border border-red-200 rounded-lg bg-red-50">
      <h3 class="font-medium text-red-600 mb-2">Failed to fetch institutions</h3>
      <p class="text-red-600">Unable to load bank list. Please check your GoCardless configuration and try again.</p>
    </div>
  <% else %>
      <form action="/gocardless_items" method="post" class="space-y-6" data-turbo="false" data-controller="bank-selector">
        <%= hidden_field_tag :authenticity_token, form_authenticity_token %>

        <!-- Step 1: Bank Selection -->
        <div>
          <h2 class="text-lg font-medium text-primary mb-4">
            <strong>Step 1:</strong> Choose your bank
          </h2>

          <div class="grid gap-3 max-h-96 overflow-y-auto">
            <% @institutions.each do |institution| %>
              <div 
                data-bank-selector-target="option"
                data-action="click->bank-selector#selectBank"
                class="border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200 relative"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <% if institution[:logo] %>
                      <img src="<%= institution[:logo] %>" alt="<%= institution[:name] || 'Bank logo' %>" class="w-8 h-8 object-contain">
                    <% else %>
                      <div class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                        <span class="text-xs font-medium text-gray-600"><%= (institution[:name] || 'Bank')&.first || 'B' %></span>
                      </div>
                    <% end %>
                    
                    <div>
                      <h3 class="font-medium text-gray-900"><%= institution[:name] || 'Unknown Bank' %></h3>
                      <% if institution[:countries] %>
                        <p class="text-sm text-gray-500">Available in: <%= institution[:countries].join(', ') %></p>
                      <% end %>
                    </div>
                  </div>
                  
                  <!-- Radio button and checkmark -->
                  <div class="flex items-center space-x-2">
                    <%= radio_button_tag 'institution_id', institution[:id], false, 
                        { 
                          "data-bank-selector-target" => "radio",
                          "data-action" => "change->bank-selector#radioChanged",
                          class: "w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                        } %>
                    
                    <!-- Checkmark (hidden by default) -->
                    <div class="selected-checkmark opacity-0 transition-opacity duration-200">
                      <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Step 2: Submit Button -->
        <div class="border-t pt-6">
          <div class="text-center">
            <p id="selection_hint" class="text-sm text-secondary mb-4">
              <strong>Please select a bank above to continue</strong>
            </p>
            
            <input type="submit" value="Connect Bank Account"
                   data-bank-selector-target="submit"
                   class="button-bg-primary hover:button-bg-primary-hover text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 cursor-pointer opacity-50 cursor-not-allowed"
                   disabled />
          </div>
        </div>
      </form>
    </div>

    <!-- About Section -->
    <div class="mt-8 p-4 bg-gray-50 rounded-lg">
      <h3 class="font-medium text-gray-900 mb-2">About GoCardless Open Banking</h3>
      <ul class="text-sm text-gray-600 space-y-1">
        <li>• Secure connection to your bank account</li>
        <li>• Read-only access to transactions and balances</li>
        <li>• No ability to move money or make payments</li>
        <li>• You can revoke access at any time</li>
      </ul>
    </div>
  <% end %>