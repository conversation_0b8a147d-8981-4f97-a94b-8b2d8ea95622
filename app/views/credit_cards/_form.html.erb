<%# locals: (account:, url:) %>

<%= render "accounts/form", account: account, url: url do |form| %>
  <%= render "shared/ruler", classes: "my-4" %>

  <div class="space-y-2">
    <%= form.fields_for :accountable do |credit_card_form| %>
      <div class="flex items-center gap-2">
        <%= credit_card_form.number_field :available_credit,
                                        label: t("credit_cards.form.available_credit"),
                                        placeholder: t("credit_cards.form.available_credit_placeholder"),
                                        min: 0 %>
      </div>

      <div class="flex items-center gap-2">
        <%= credit_card_form.money_field :minimum_payment,
                                        label: t("credit_cards.form.minimum_payment"),
                                        placeholder: t("credit_cards.form.minimum_payment_placeholder"),
                                        default_currency: Current.family.currency %>

        <%= credit_card_form.number_field :apr,
                                        label: t("credit_cards.form.apr"),
                                        placeholder: t("credit_cards.form.apr_placeholder"),
                                        min: 0,
                                        step: 0.01 %>
      </div>

      <div class="flex items-center gap-2">
        <%= credit_card_form.date_field :expiration_date,
                                      label: t("credit_cards.form.expiration_date") %>
        <%= credit_card_form.number_field :annual_fee,
                                        label: t("credit_cards.form.annual_fee"),
                                        placeholder: t("credit_cards.form.annual_fee_placeholder"),
                                        min: 0 %>
      </div>
    <% end %>
  </div>
<% end %>
