<header class="flex items-center justify-between">
  <h1 class="text-primary text-xl font-medium">Merchants</h1>

  <%= render LinkComponent.new(
    text: "New merchant",
    variant: "primary",
    href: new_family_merchant_path,
    frame: :modal
  ) %>
</header>

<div class="bg-container rounded-xl shadow-border-xs p-4">
  <% if @family_merchants.any? %>
    <div class="rounded-xl bg-container-inset space-y-1 p-1">
      <div class="flex items-center gap-1.5 px-4 py-2 text-xs font-medium text-secondary uppercase">
        <p><%= t(".title") %></p>
        <span class="text-subdued">&middot;</span>
        <p><%= @family_merchants.count %></p>
      </div>

      <div class="bg-container rounded-lg shadow-border-xs">
        <div class="overflow-hidden rounded-lg">
          <%= render partial: "family_merchants/family_merchant", collection: @family_merchants, spacer_template: "shared/ruler" %>
        </div>
      </div>
    </div>
  <% else %>
    <div class="flex justify-center items-center py-20">
      <div class="text-center flex flex-col items-center max-w-[300px]">
        <p class="text-primary mb-1 font-medium text-sm"><%= t(".empty") %></p>

        <%= render LinkComponent.new(
          text: t(".new"),
          icon: "plus",
          href: new_family_merchant_path,
          frame: :modal
        ) %>
      </div>
    </div>
  <% end %>
</div>
