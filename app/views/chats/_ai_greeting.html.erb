<div class="flex items-start w-full gap-3 p-2">
  <%= render "chats/ai_avatar" %>

  <div class="max-w-[85%] text-sm space-y-4 text-primary">
    <p>Hey <%= Current.user&.first_name || "there" %>! I'm an AI built by <PERSON> to help with your finances. I have access to the web and your account data.</p>

    <p>
      You can use <span class="bg-container border border-secondary px-1.5 py-0.5 rounded font-mono text-xs">/</span> to access commands
    </p>

    <div class="space-y-3">
      <p>Here's a few questions you can ask:</p>

      <% questions = [
        {
          icon: "chart-area",
          text: "Evaluate investment portfolio"
        },
        {
          icon: "wallet-minimal",
          text: "Show spending insights"
        },
        {
          icon: "alert-triangle",
          text: "Find unusual patterns"
        }
      ] %>

      <div class="space-y-2.5">
        <% questions.each do |question| %>
          <button data-action="chat#submitSampleQuestion"
                  data-chat-question-param="<%= question[:text] %>"
                  class="w-fit flex items-center gap-2 border border-tertiary rounded-full py-1.5 px-2.5 hover:bg-gray-100">
            <%= icon(question[:icon]) %> <%= question[:text] %>
          </button>
        <% end %>
      </div>
    </div>
  </div>
</div>
