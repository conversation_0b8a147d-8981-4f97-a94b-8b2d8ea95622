<div data-controller="chat hotkey">
  <%= turbo_frame_tag chat_frame do %>
    <%= turbo_stream_from @chat %>

    <h1 class="sr-only"><%= @chat.title %></h1>

    <div class="flex flex-col h-full">
      <div class="md:p-4">
        <%= render "chats/chat_nav", chat: @chat %>
      </div>

      <div id="messages" class="grow overflow-y-auto p-4 space-y-6 pb-24 lg:pb-4" data-chat-target="messages">
        <% if @chat.conversation_messages.any? %>
          <% @chat.conversation_messages.ordered.each do |message| %>
            <%= render message %>
          <% end %>
        <% else %>
          <div class="mt-auto">
            <%= render "chats/ai_greeting", context: "chat" %>
          </div>
        <% end %>

        <% if params[:thinking].present? %>
          <%= render "chats/thinking_indicator", chat: @chat %>
        <% end %>

        <% if @chat.error.present? && @chat.needs_assistant_response? %>
          <%= render "chats/error", chat: @chat %>
        <% end %>
      </div>

      <%# DESKTOP - Chat form %>
      <div class="p-4 pt-0 lg:mt-auto fixed lg:static left-0 bottom-16 w-full bg-surface">
        <%= render "messages/chat_form", chat: @chat %>
      </div>
    </div>
  <% end %>
</div>
