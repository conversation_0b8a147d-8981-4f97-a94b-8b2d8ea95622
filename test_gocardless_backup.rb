#!/usr/bin/env ruby

# Test script for GoCardless backup provider configuration
# Run with: ruby test_gocardless_backup.rb

require 'bundler/setup'
require 'dotenv/load'
require_relative 'config/environment'

puts "=== GoCardless Backup Provider Test ==="
puts

# Check environment variables
puts "=== Environment Configuration ==="
puts "Primary Provider:"
puts "  GOCARDLESS_SECRET_ID: #{ENV['GOCARDLESS_SECRET_ID'] ? 'SET' : 'NOT SET'}"
puts "  GOCARDLESS_SECRET_KEY: #{ENV['GOCARDLESS_SECRET_KEY'] ? 'SET' : 'NOT SET'}"
puts "  GOCARDLESS_ENV: #{ENV['GOCARDLESS_ENV'] || 'NOT SET (defaults to sandbox)'}"
puts

puts "Backup Provider:"
puts "  GOCARDLESS_BACKUP_SECRET_ID: #{ENV['GOCARDLESS_BACKUP_SECRET_ID'] ? 'SET' : 'NOT SET'}"
puts "  GOCARDLESS_BACKUP_SECRET_KEY: #{ENV['GOCARDLESS_BACKUP_SECRET_KEY'] ? 'SET' : 'NOT SET'}"
puts "  GOCARDLESS_BACKUP_ENV: #{ENV['GOCARDLESS_BACKUP_ENV'] || 'NOT SET (defaults to GOCARDLESS_ENV)'}"
puts

puts "Third Backup Provider:"
puts "  GOCARDLESS_BACKUP2_SECRET_ID: #{ENV['GOCARDLESS_BACKUP2_SECRET_ID'] ? 'SET' : 'NOT SET'}"
puts "  GOCARDLESS_BACKUP2_SECRET_KEY: #{ENV['GOCARDLESS_BACKUP2_SECRET_KEY'] ? 'SET' : 'NOT SET'}"
puts "  GOCARDLESS_BACKUP2_ENV: #{ENV['GOCARDLESS_BACKUP2_ENV'] || 'NOT SET (defaults to GOCARDLESS_ENV)'}"
puts

# Check Rails configuration
puts "=== Rails Configuration ==="
providers = Rails.application.config.gocardless_providers || []

puts "Total providers configured: #{providers.count}"
providers.each_with_index do |provider, index|
  puts "  Provider #{index + 1} (#{provider[:name]}): CONFIGURED"
end
puts

if providers.empty?
  puts "❌ No GoCardless providers configured"
  exit 1
end

# Test provider registry
puts "=== Provider Registry Test ==="
begin
  provider = Provider::Registry.get_provider(:gocardless)
  
  if provider.is_a?(Provider::GocardlessManager)
    puts "✅ Using GoCardless Manager (multiple providers available)"
    puts "   Provider count: #{provider.provider_count}"
    puts "   Current provider: #{provider.current_provider.class.name}"
    puts "   Status: #{provider.provider_status}"
  elsif provider.is_a?(Provider::Gocardless)
    puts "✅ Using single GoCardless provider (no backups configured)"
  else
    puts "❌ Unexpected provider type: #{provider.class.name}"
  end
rescue => e
  puts "❌ Provider registry error: #{e.message}"
  exit 1
end
puts

# Test provider health
puts "=== Provider Health Test ==="
begin
  if provider.healthy?
    puts "✅ Provider is healthy"
  else
    puts "❌ Provider health check failed"
  end
rescue => e
  puts "❌ Health check error: #{e.message}"
end
puts

# Test authentication
puts "=== Authentication Test ==="
begin
  response = provider.get_access_token
  
  if response.success?
    puts "✅ Authentication successful"
    if provider.is_a?(Provider::GocardlessManager)
      puts "   Current provider: #{provider.current_provider.class.name}"
    end
  else
    puts "❌ Authentication failed: #{response.error&.message}"
  end
rescue => e
  puts "❌ Authentication error: #{e.message}"
end
puts

# Test institutions endpoint (light test)
puts "=== Institutions Endpoint Test ==="
begin
  response = provider.list_institutions(country: "GB")
  
  if response.success?
    institutions = response.data
    puts "✅ Institutions endpoint working"
    puts "   Found #{institutions.count} institutions"
    
    if provider.is_a?(Provider::GocardlessManager)
      puts "   Provider status: #{provider.provider_status}"
    end
  else
    puts "❌ Institutions endpoint failed: #{response.error&.message}"
  end
rescue => e
  puts "❌ Institutions endpoint error: #{e.message}"
end
puts

# Test failover (if multiple providers are available)
if providers.count > 1 && provider.is_a?(Provider::GocardlessManager)
  puts "=== Failover Test ==="
  begin
    puts "Testing provider switching..."
    original_provider = provider.current_provider.class.name
    
    if provider.switch_to_next_provider
      new_provider = provider.current_provider.class.name
      puts "✅ Successfully switched providers"
      puts "   From: #{original_provider}"
      puts "   To: #{new_provider}"
      
      # Test that the new provider works
      response = provider.get_access_token
      if response.success?
        puts "✅ Backup provider authentication successful"
      else
        puts "❌ Backup provider authentication failed: #{response.error&.message}"
      end
      
      # Reset to primary
      provider.reset_to_primary_provider
      puts "✅ Reset to primary provider"
    else
      puts "❌ Failed to switch providers"
    end
  rescue => e
    puts "❌ Failover test error: #{e.message}"
  end
  puts
end

puts "=== Test Complete ==="

if providers.count > 1
  puts "✅ GoCardless multi-provider system is configured and working"
  puts "   #{providers.count} providers available for automatic failover"
  puts "   The system will automatically failover when rate limits are hit"
else
  puts "ℹ️  GoCardless backup providers not configured"
  puts "   Add GOCARDLESS_BACKUP_SECRET_ID/KEY and GOCARDLESS_BACKUP2_SECRET_ID/KEY to enable failover"
end
