GIT
  remote: https://github.com/maybe-finance/lucide-rails.git
  revision: 272e5fb8418ea458da3995d6abe0ba0ceee9c9f0
  specs:
    lucide-rails (0.2.0)
      railties (>= 4.1.0)

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.1)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (2.2.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    after_commit_everywhere (1.6.0)
      activerecord (>= 4.2)
      activesupport
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1113.0)
    aws-sdk-core (3.225.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.104.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.177.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    benchmark-ips (2.14.0)
    better_html (2.1.1)
      actionview (>= 6.0)
      activesupport (>= 6.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      parser (>= 2.4)
      smart_properties
    bigdecimal (3.2.2)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    childprocess (5.1.0)
      logger (~> 1.5)
    chunky_png (1.4.0)
    climate_control (1.2.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    css_parser (1.21.1)
      addressable
    csv (3.3.5)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    derailed_benchmarks (2.2.1)
      base64
      benchmark-ips (~> 2)
      bigdecimal
      drb
      get_process_mem
      heapy (~> 0)
      logger
      memory_profiler (>= 0, < 2)
      mini_histogram (>= 0.3.0)
      mutex_m
      ostruct
      rack (>= 1)
      rack-test
      rake (> 10, < 14)
      ruby-statistics (>= 4.0.1)
      ruby2_keywords
      thor (>= 0.19, < 2)
    docile (1.4.1)
    doorkeeper (5.8.2)
      railties (>= 5)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    drb (2.2.3)
    erb (5.0.1)
    erb_lint (0.9.0)
      activesupport
      better_html (>= 2.0.1)
      parser (>= *******)
      rainbow
      rubocop (>= 1)
      smart_properties
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    foreman (0.88.1)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    get_process_mem (1.0.0)
      bigdecimal (>= 2.0)
      ffi (~> 1.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashdiff (1.2.0)
    heapy (0.2.0)
      thor
    highline (3.1.2)
      reline
    hotwire-livereload (2.0.0)
      actioncable (>= 7.0.0)
      listen (>= 3.0.0)
      railties (>= 7.0.0)
    hotwire_combobox (0.4.0)
      platform_agent (>= 1.0.1)
      rails (>= *******)
      stimulus-rails (>= 1.2)
      turbo-rails (>= 1.2)
    htmlbeautifier (1.4.3)
    htmlentities (4.3.4)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18n-tasks (1.0.15)
      activesupport (>= 4.0.2)
      ast (>= 2.1.0)
      erubi
      highline (>= 2.0.0)
      i18n
      parser (>= *******)
      rails-i18n
      rainbow (>= 2.2.2, < 4.0)
      ruby-progressbar (~> 1.8, >= 1.8.1)
      terminal-table (>= 1.5.1)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    inline_svg (1.10.0)
      activesupport (>= 3.0)
      nokogiri (>= 1.6)
    intercom-rails (1.0.6)
      activesupport (> 4.0)
      jwt (~> 2.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.12.2)
    jwt (2.10.1)
      base64
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    logtail (0.1.17)
      msgpack (~> 1.0)
    logtail-rack (0.2.6)
      logtail (~> 0.1)
      rack (>= 1.2, < 4.0)
    logtail-rails (0.2.10)
      actionpack (>= 5.0.0)
      activerecord (>= 5.0.0)
      logtail (~> 0.1, >= 0.1.14)
      logtail-rack (~> 0.1)
      railties (>= 5.0.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lookbook (2.3.11)
      activemodel
      css_parser
      htmlbeautifier (~> 1.3)
      htmlentities (~> 4.3.4)
      marcel (~> 1.0)
      railties (>= 5.0)
      redcarpet (~> 3.5)
      rouge (>= 3.26, < 5.0)
      view_component (>= 2.0)
      yard (~> 0.9)
      zeitwerk (~> 2.5)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memory_profiler (1.1.0)
    method_source (1.1.0)
    mini_histogram (0.3.1)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    mocha (2.7.1)
      ruby2_keywords (>= 0.0.5)
    msgpack (1.8.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    octokit (10.0.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    ostruct (0.6.2)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    plaid (41.0.0)
      faraday (>= 1.0.1, < 3.0)
      faraday-multipart (>= 1.0.1, < 2.0)
    platform_agent (1.0.1)
      activesupport (>= 5.2.0)
      useragent (~> 0.16.3)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-mini-profiler (4.0.0)
      rack (>= 1.2.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.10)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    rails-settings-cached (2.9.6)
      activerecord (>= 5.0.0)
      railties (>= 5.0.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbs (3.9.4)
      logger
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    redcarpet (3.6.1)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rexml (3.4.1)
    rotp (6.3.0)
    rouge (4.5.2)
    rqrcode (3.1.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 2.0)
    rqrcode_core (2.0.0)
    rubocop (1.76.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-lsp (0.24.1)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 5)
      sorbet-runtime (>= 0.5.10782)
    ruby-lsp-rails (0.4.6)
      ruby-lsp (>= 0.24.0, < 0.25.0)
    ruby-openai (8.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-statistics (4.1.0)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    securerandom (0.4.1)
    selenium-webdriver (4.33.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sentry-rails (5.25.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.25.0)
    sentry-ruby (5.25.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sentry-sidekiq (5.25.0)
      sentry-ruby (~> 5.25.0)
      sidekiq (>= 3.0)
    sidekiq (8.0.4)
      connection_pool (>= 2.5.0)
      json (>= 2.9.0)
      logger (>= 1.6.2)
      rack (>= 3.1.0)
      redis-client (>= 0.23.2)
    sidekiq-cron (2.3.0)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    skylight (6.0.4)
      activesupport (>= 5.2.0)
    smart_properties (1.17.0)
    sorbet-runtime (0.5.12163)
    stackprof (0.2.27)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    stripe (15.2.1)
    tailwindcss-rails (4.2.3)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.8)
    tailwindcss-ruby (4.1.8-aarch64-linux-gnu)
    tailwindcss-ruby (4.1.8-aarch64-linux-musl)
    tailwindcss-ruby (4.1.8-arm64-darwin)
    tailwindcss-ruby (4.1.8-x86_64-darwin)
    tailwindcss-ruby (4.1.8-x86_64-linux-gnu)
    tailwindcss-ruby (4.1.8-x86_64-linux-musl)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode (*******)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    vcr (6.3.1)
      base64
    vernier (1.8.0)
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.37)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-darwin
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  aasm
  activerecord-import
  after_commit_everywhere (~> 1.0)
  aws-sdk-s3 (~> 1.177.0)
  bcrypt (~> 3.1)
  benchmark-ips
  bootsnap
  brakeman
  capybara
  climate_control
  csv
  debug
  derailed_benchmarks
  doorkeeper
  dotenv-rails
  erb_lint
  faker
  faraday
  faraday-multipart
  faraday-retry
  foreman
  hotwire-livereload
  hotwire_combobox
  httparty
  i18n-tasks
  image_processing (>= 1.2)
  importmap-rails
  inline_svg
  intercom-rails
  jbuilder
  jwt
  letter_opener
  logtail-rails
  lookbook (= 2.3.11)
  lucide-rails!
  mocha
  octokit
  ostruct
  pagy
  pg (~> 1.5)
  plaid
  propshaft
  puma (>= 5.0)
  rack-attack (~> 6.6)
  rack-mini-profiler
  rails (~> 7.2.2)
  rails-settings-cached
  redcarpet
  redis (~> 5.4)
  rotp (~> 6.3)
  rqrcode (~> 3.0)
  rubocop-rails-omakase
  ruby-lsp-rails
  ruby-openai
  selenium-webdriver
  sentry-rails
  sentry-ruby
  sentry-sidekiq
  sidekiq
  sidekiq-cron
  simplecov
  skylight
  stackprof
  stimulus-rails
  stripe
  tailwindcss-rails
  turbo-rails
  tzinfo-data
  vcr
  vernier
  view_component
  web-console
  webmock

RUBY VERSION
   ruby 3.4.4p34

BUNDLED WITH
   2.6.9
