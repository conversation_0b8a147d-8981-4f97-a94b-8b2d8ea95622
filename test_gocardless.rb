#!/usr/bin/env ruby

# Test script to verify GoCardless configuration and API connectivity
require 'net/http'
require 'json'
require 'uri'

# Load environment variables from .env.local
env_file = File.join(__dir__, '.env.local')
if File.exist?(env_file)
  File.readlines(env_file).each do |line|
    line = line.strip
    next if line.empty? || line.start_with?('#')
    key, value = line.split('=', 2)
    ENV[key] = value if key && value
  end
end

puts "=== GoCardless Configuration Test ==="
puts "GOCARDLESS_SECRET_ID: #{ENV['GOCARDLESS_SECRET_ID'] ? 'SET' : 'NOT SET'}"
puts "GOCARDLESS_SECRET_KEY: #{ENV['GOCARDLESS_SECRET_KEY'] ? 'SET' : 'NOT SET'}"
puts "GOCARDLESS_ENV: #{ENV['GOCARDLESS_ENV'] || 'NOT SET'}"
puts

if ENV['GOCARDLESS_SECRET_ID'].nil? || ENV['GOCARDLESS_SECRET_KEY'].nil?
  puts "❌ GoCardless credentials not configured"
  exit 1
end

# Test API connectivity
BASE_URL = "https://bankaccountdata.gocardless.com/api/v2"

def make_request(method, path, body = nil, headers = {})
  uri = URI("#{BASE_URL}/#{path}")

  case method.upcase
  when 'POST'
    request = Net::HTTP::Post.new(uri)
  when 'GET'
    request = Net::HTTP::Get.new(uri)
  else
    raise "Unsupported method: #{method}"
  end

  request['Content-Type'] = 'application/json'
  headers.each { |k, v| request[k] = v }
  request.body = body.to_json if body

  response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
    http.request(request)
  end

  {
    status: response.code.to_i,
    body: response.body.empty? ? {} : JSON.parse(response.body)
  }
rescue JSON::ParserError
  {
    status: response.code.to_i,
    body: response.body
  }
end

puts "=== Testing GoCardless API Authentication ==="

begin
  # Test authentication
  response = make_request('POST', 'token/new/', {
    secret_id: ENV['GOCARDLESS_SECRET_ID'],
    secret_key: ENV['GOCARDLESS_SECRET_KEY']
  })

  if response[:status] == 200
    puts "✅ Authentication successful"
    access_token = response[:body]["access"]
    puts "Access token received: #{access_token[0..20]}..."

    # Test institutions endpoint
    puts "\n=== Testing Institutions Endpoint ==="
    institutions_response = make_request('GET', 'institutions/?country=GB', nil, {
      'Authorization' => "Bearer #{access_token}"
    })

    if institutions_response[:status] == 200
      institutions = institutions_response[:body]
      puts "✅ Institutions endpoint working"
      puts "Found #{institutions.length} institutions"

      # Find sandbox institution
      sandbox_institution = institutions.find { |inst| inst["name"]&.downcase&.include?("sandbox") }
      test_institution = institutions.find { |inst| inst["name"]&.downcase&.include?("test") }

      if sandbox_institution
        puts "✅ Found sandbox institution: #{sandbox_institution['name']} (#{sandbox_institution['id']})"
      elsif test_institution
        puts "✅ Found test institution: #{test_institution['name']} (#{test_institution['id']})"
      else
        puts "⚠️  No sandbox/test institution found"
        puts "Looking for institutions with 'sandbox' or 'test' in name..."

        # Search for any institution that might be for testing
        test_candidates = institutions.select do |inst|
          name = inst["name"]&.downcase || ""
          name.include?("sandbox") || name.include?("test") || name.include?("demo") ||
          name.include?("mock") || name.include?("sample") || inst["id"]&.include?("SANDBOX")
        end

        if test_candidates.any?
          puts "Found potential test institutions:"
          test_candidates.each do |inst|
            puts "  - #{inst['name']} (#{inst['id']})"
          end
        else
          puts "No test institutions found. First 10 institutions:"
          institutions.first(10).each do |inst|
            puts "  - #{inst['name']} (#{inst['id']})"
          end
        end
      end
    else
      puts "❌ Institutions endpoint failed: #{institutions_response[:status]}"
      puts "Response: #{institutions_response[:body]}"
    end

  else
    puts "❌ Authentication failed: #{response[:status]}"
    puts "Response: #{response[:body]}"
  end

rescue => e
  puts "❌ API Error: #{e.class}: #{e.message}"
end
